name: Base Run
on:
  pull_request:
    paths:
      - 'tf/dev/1_base/**'
      - 'tf/prod/1_base/**'

env:
  PATH_TF_DEV: tf/dev/1_base
  PATH_TF_PROD: tf/prod/1_base
  MONGODB_ATLAS_PUBLIC_KEY: ${{ vars.MONGODB_ATLAS_PUBLIC_KEY }}
  MONGODB_ATLAS_PRIVATE_KEY: ${{ secrets.MONGODB_ATLAS_PRIVATE_KEY }}
  VAULT_ADDR: https://vault.dev.is360.io:8200

jobs:
  plan-dev:
    name: Plan Dev
    runs-on: [dev-fe]
    environment: owners
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Retrieve secret from <PERSON><PERSON>
        uses: hashicorp/vault-action@v3
        with:
          exportToken: true
          method: jwt
          url: ${{ env.VAULT_ADDR }}
          role: gha
          secrets: |
            dev/data/dev-dp dp-kif-user-key | DP_KIF_USER_KEY ;
      - name: Add github.com to known hosts
        run: |
          mkdir -p ~/.ssh
          ssh-keyscan github.com >> ~/.ssh/known_hosts
      - name: Setup SSH Agent
        id: ssh-setup
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: |
            ${{ secrets.SSH_INFRABOUNDARYVAULT_PK }}
      - name: Checkout
        id: checkout
        uses: actions/checkout@v4
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 20
      - name: Setup Terraform
        id: setup-terraform
        uses: hashicorp/setup-terraform@v3
      - name: Terragrunt Plan Dev
        id: terragrunt-plan-dev
        run: |
          cd ${{ env.PATH_TF_DEV }}
          terragrunt init
          terragrunt plan
      - name: Revoke token
        if: always()
        run: |
          curl -X POST -sv -H "X-Vault-Token: ${{ env.VAULT_TOKEN }}" ${{ env.VAULT_ADDR }}/v1/auth/token/revoke-self

  apply-dev:
    name: Plan Dev
    runs-on: [dev-fe]
    environment: owners-dev
    needs: plan-dev
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Retrieve secret from Vault
        uses: hashicorp/vault-action@v3
        with:
          exportToken: true
          method: jwt
          url: ${{ env.VAULT_ADDR }}
          role: gha
          secrets: |
            dev/data/dev-dp dp-kif-user-key | DP_KIF_USER_KEY ;
      - name: Add github.com to known hosts
        run: |
          mkdir -p ~/.ssh
          ssh-keyscan github.com >> ~/.ssh/known_hosts
      - name: Setup SSH Agent
        id: ssh-setup
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: |
            ${{ secrets.SSH_INFRABOUNDARYVAULT_PK }}
      - name: Checkout
        id: checkout
        uses: actions/checkout@v4
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 20
      - name: Setup Terraform
        id: setup-terraform
        uses: hashicorp/setup-terraform@v3
      - name: Terragrunt Plan Dev
        id: terragrunt-plan-dev
        run: |
          cd ${{ env.PATH_TF_DEV }}
          terragrunt apply --auto-approve
      - name: Revoke token
        if: always()
        run: |
          curl -X POST -sv -H "X-Vault-Token: ${{ env.VAULT_TOKEN }}" ${{ env.VAULT_ADDR }}/v1/auth/token/revoke-self

  plan-prod:
    name: Plan Prod
    runs-on: [prod-fe-fe-infra]
    needs: run-dev
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Retrieve secret from Vault
        uses: hashicorp/vault-action@v3
        with:
          exportToken: true
          method: jwt
          url: ${{ env.VAULT_ADDR }}
          role: gha
          secrets: |
            dev/data/dev-dp dp-kif-user-key | DP_KIF_USER_KEY ;
      - name: Add github.com to known hosts
        run: |
          mkdir -p ~/.ssh
          ssh-keyscan github.com >> ~/.ssh/known_hosts
      - name: Setup SSH Agent
        id: ssh-setup
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: |
            ${{ secrets.SSH_INFRABOUNDARYVAULT_PK }}
      - name: Checkout
        id: checkout
        uses: actions/checkout@v4
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 20
      - name: Setup Terraform
        id: setup-terraform
        uses: hashicorp/setup-terraform@v3
      - name: Terragrunt Plan Prod
        id: terragrunt-plan-prod
        run: |
          cd ${{ env.PATH_TF_PROD }}
          terragrunt init
          terragrunt plan
      - name: Revoke token
        if: always()
        run: |
          curl -X POST -sv -H "X-Vault-Token: ${{ env.VAULT_TOKEN }}" ${{ env.VAULT_ADDR }}/v1/auth/token/revoke-self

  apply-prod:
    name: Plan Prod
    runs-on: [prod-fe-fe-infra]
    needs: run-dev
    environment: owners-prod
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Retrieve secret from Vault
        uses: hashicorp/vault-action@v3
        with:
          exportToken: true
          method: jwt
          url: ${{ env.VAULT_ADDR }}
          role: gha
          secrets: |
            dev/data/dev-dp dp-kif-user-key | DP_KIF_USER_KEY ;
      - name: Add github.com to known hosts
        run: |
          mkdir -p ~/.ssh
          ssh-keyscan github.com >> ~/.ssh/known_hosts
      - name: Setup SSH Agent
        id: ssh-setup
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: |
            ${{ secrets.SSH_INFRABOUNDARYVAULT_PK }}
      - name: Checkout
        id: checkout
        uses: actions/checkout@v4
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 20
      - name: Setup Terraform
        id: setup-terraform
        uses: hashicorp/setup-terraform@v3
      - name: Terragrunt Plan Prod
        id: terragrunt-plan-prod
        run: |
          cd ${{ env.PATH_TF_PROD }}
          terragrunt apply --auto-approve
      - name: Revoke token
        if: always()
        run: |
          curl -X POST -sv -H "X-Vault-Token: ${{ env.VAULT_TOKEN }}" ${{ env.VAULT_ADDR }}/v1/auth/token/revoke-self