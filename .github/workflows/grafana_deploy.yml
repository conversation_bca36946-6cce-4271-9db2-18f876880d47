name: Deploy Grafana Boards

on:
  workflow_dispatch:

jobs:
  playground-plan:
    name: Run Playground Grafana Dev Plan
    runs-on: [dev-fe]
    steps:
      - name: Setup SSH Agent
        id: ssh-setup
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: |
             ${{ secrets.SSH_INFRACOM_PK_240414 }}
      - name: Add GitHub.com keys to known_hosts
        run: ssh-keyscan github.com >> ~/.ssh/known_hosts
      - name: Checkout
        uses: actions/checkout@v4
      - name: Collect Git and SSH config files in a directory that is part of the Docker build context
        run: |
          mkdir -p build-context/
          cp -r ~/.gitconfig  ~/.ssh build-context/
          sed 's|/home/<USER>/root|g' -i.bak build-context/.ssh/config

      - name: Setup Node # Prerequisite for Terraform
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Setup Terraform
        id: setup-terraform
        uses: hashicorp/setup-terraform@v3

      - name: Execute Terraform NA Plan
        id: terraform
        run: |
          cd tf/envs/dev/us-east-1/5_obs/grafana
          terraform init -var="grafana_auth=${{ secrets.GRAFANA_AUTH }}" -var="grafana_url=${{ secrets.GRAFANA_URL }}" -upgrade
          terraform plan -var="grafana_auth=${{ secrets.GRAFANA_AUTH }}" -var="grafana_url=${{ secrets.GRAFANA_URL }}" -out=tfplangrafanadev
          echo "Terraform plan completed. Checking for tfplan file..."
      
      - name: Execute Terraform EU Plan
        id: terraform-eu-plan
        run: |
          cd tf/envs/dev/eu-central-1/5_obs/grafana
          terraform init -var="grafana_auth=${{ secrets.GRAFANA_AUTH }}" -var="grafana_url=${{ secrets.GRAFANA_URL }}" -upgrade
          terraform plan -var="grafana_auth=${{ secrets.GRAFANA_AUTH }}" -var="grafana_url=${{ secrets.GRAFANA_URL }}" -out=tfplangrafanadeveu
          echo "Terraform plan completed. Checking for tfplan file..."

      - name: Upload Terraform Plan
        uses: actions/upload-artifact@v4
        with:
          name: tfplangrafanadev
          path: |
            tf/envs/dev/us-east-1/5_obs/grafana/tfplangrafanadev
            tf/envs/dev/eu-central-1/5_obs/grafana/tfplangrafanadeveu

  playground-apply:
    name: Apply Playground Grafana Dev
    runs-on: [dev-fe]
    needs: playground-plan
    environment: dev
    steps:
      - name: Setup SSH Agent
        id: ssh-setup
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: |
             ${{ secrets.SSH_INFRACOM_PK_240414 }}
      - name: Add GitHub.com keys to known_hosts
        run: ssh-keyscan github.com >> ~/.ssh/known_hosts
      - name: Checkout
        uses: actions/checkout@v4
      - name: Collect Git and SSH config files in a directory that is part of the Docker build context
        run: |
          mkdir -p build-context/
          cp -r ~/.gitconfig  ~/.ssh build-context/
          sed 's|/home/<USER>/root|g' -i.bak build-context/.ssh/config

      - name: Setup Node # Prerequisite for Terraform
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Setup Terraform
        id: setup-terraform
        uses: hashicorp/setup-terraform@v3

      - name: Execute Terraform na Apply
        run: |
          cd tf/envs/dev/us-east-1/5_obs/grafana
          terraform init -upgrade -var="grafana_auth=${{ secrets.GRAFANA_AUTH }}" -var="grafana_url=${{ secrets.GRAFANA_URL }}"
          echo "Applying Terraform plan..."
          terraform apply -var="grafana_auth=${{ secrets.GRAFANA_AUTH }}" -var="grafana_url=${{ secrets.GRAFANA_URL }}" --auto-approve

      - name: Execute Terraform eu Apply
        run: |
          cd tf/envs/dev/eu-central-1/5_obs/grafana
          terraform init -upgrade -var="grafana_auth=${{ secrets.GRAFANA_AUTH }}" -var="grafana_url=${{ secrets.GRAFANA_URL }}"
          echo "Applying Terraform plan..."
          terraform apply -var="grafana_auth=${{ secrets.GRAFANA_AUTH }}" -var="grafana_url=${{ secrets.GRAFANA_URL }}" --auto-approve

  prod-plan:
    name: Plan Grafana Prod changes
    runs-on: [prod-fe-fe-infra]
    if: contains(github.ref, 'refs/heads/main') # Only run on main branch
    steps:
      - name: Setup SSH Agent
        id: ssh-setup
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: |
             ${{ secrets.SSH_INFRACOM_PK_240414 }}
      - name: Add GitHub.com keys to known_hosts
        run: ssh-keyscan github.com >> ~/.ssh/known_hosts
      - name: Checkout
        uses: actions/checkout@v4
      - name: Collect Git and SSH config files in a directory that is part of the Docker build context
        run: |
          mkdir -p build-context/
          cp -r ~/.gitconfig  ~/.ssh build-context/
          sed 's|/home/<USER>/root|g' -i.bak build-context/.ssh/config

      - name: Setup Node # Prerequisite for Terraform
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Setup Terraform
        id: setup-terraform
        uses: hashicorp/setup-terraform@v3

      - name: Execute Terraform NA Plan
        id: terraform
        run: |
          cd tf/envs/prod/us-east-1/5_obs/grafana
          terraform init -var="grafana_auth=${{ secrets.GRAFANA_AUTH }}" -var="grafana_url=${{ secrets.GRAFANA_URL }}" -upgrade
          terraform plan -var="grafana_auth=${{ secrets.GRAFANA_AUTH }}" -var="grafana_url=${{ secrets.GRAFANA_URL }}" -out=tfplangrafanadev
          echo "Terraform plan completed. Checking for tfplan file..."
      
      - name: Execute Terraform EU Plan
        id: terraform-prod-eu-plan
        run: |
          cd tf/envs/prod/eu-central-1/5_obs/grafana
          terraform init -var="grafana_auth=${{ secrets.GRAFANA_AUTH }}" -var="grafana_url=${{ secrets.GRAFANA_URL }}" -upgrade
          terraform plan -var="grafana_auth=${{ secrets.GRAFANA_AUTH }}" -var="grafana_url=${{ secrets.GRAFANA_URL }}" -out=tfplangrafanadeveu
          echo "Terraform plan completed. Checking for tfplan file..."

      - name: Upload Terraform Plan
        uses: actions/upload-artifact@v4
        with:
          name: tfplangrafanaprod
          path: |
            tf/envs/prod/us-east-1/5_obs/grafana/tfplangrafanadev
            tf/envs/prod/eu-central-1/5_obs/grafana/tfplangrafanadeveu  

  prod-apply:
    name: Apply Grafana prod changes
    runs-on: [prod-fe-fe-infra]
    needs: prod-plan
    if: contains(github.ref, 'refs/heads/main') # Only run on main branch
    environment: prod
    steps:
      - name: Setup SSH Agent
        id: ssh-setup
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: |
             ${{ secrets.SSH_INFRACOM_PK_240414 }}
      - name: Add GitHub.com keys to known_hosts
        run: ssh-keyscan github.com >> ~/.ssh/known_hosts
      - name: Checkout
        uses: actions/checkout@v4
      - name: Collect Git and SSH config files in a directory that is part of the Docker build context
        run: |
          mkdir -p build-context/
          cp -r ~/.gitconfig  ~/.ssh build-context/
          sed 's|/home/<USER>/root|g' -i.bak build-context/.ssh/config

      - name: Setup Node # Prerequisite for Terraform
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Setup Terraform
        id: setup-terraform
        uses: hashicorp/setup-terraform@v3

      - name: Execute Terraform NA Apply
        run: |
          cd tf/envs/prod/us-east-1/5_obs/grafana
          terraform init -upgrade -var="grafana_auth=${{ secrets.PRD_GRAFANA_AUTH }}" -var="grafana_url=${{ secrets.PRD_GRAFANA_URL }}"
          echo "Applying Terraform plan..."
          terraform apply -var="grafana_auth=${{ secrets.PRD_GRAFANA_AUTH }}" -var="grafana_url=${{ secrets.PRD_GRAFANA_URL }}" --auto-approve

      - name: Execute Terraform EU Apply
        run: |
          cd tf/envs/prod/eu-central-1/5_obs/grafana
          terraform init -upgrade -var="grafana_auth=${{ secrets.PRD_GRAFANA_AUTH }}" -var="grafana_url=${{ secrets.PRD_GRAFANA_URL }}"
          echo "Applying Terraform plan..."
          terraform apply -var="grafana_auth=${{ secrets.PRD_GRAFANA_AUTH }}" -var="grafana_url=${{ secrets.PRD_GRAFANA_URL }}" --auto-approve
