# fe-infra
Infrastructure for the FE (front end) subsystem

## Summary

- [Creating MongoDB users](#creating-mongodb-users)

### Creating MongoDB users

To create new users, we are using the new Vault Dynamic Secret. Your job is to follow the next process in order to get it done

- First, create a new branch named `stage/mongousers/vx.y.z`. The version can be found in the `.tf` files for [dev](./tf/dev/10_vault_mongo_roles/main.tf#L30) and [prod](./tf/prod/10_vault_mongo_roles/main.tf#L26)

- Then add the new roles following the example of [`fe-ev-csms-api`](./tf/dev/10_vault_mongo_roles/main.tf#L40)

- Create a new pull request and wait for the validation actions and code owners approvals

- When everything looks good, tag your branch with the version you created
    ```sh
    git tag mongousers/vx.y.z -m "Adding fe-{team}-{role-name} MongoDB user"
    ```

- Push the tag to origin
    ```sh
    git push origin mongousers/vx.y.z
    ```

- The `Action` will request approval from the infra owners. If no approvals in `60 minutes` the action will expire

```mermaid
gitGraph
    commit
    commit tag: "mongouser/v0.0.1"
    branch stage/mongouser/v0.0.2
    checkout stage/mongouser/v0.0.2
    commit
    commit
    commit tag: "mongouser/v0.0.2"
    checkout main
    merge stage/mongouser/v0.0.2
    commit
    commit
```