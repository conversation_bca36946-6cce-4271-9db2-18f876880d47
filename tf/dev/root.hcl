generate "aws_provider" {
  path = "aws.tf"
  if_exists = "overwrite_terragrunt"
  contents = <<EOF
provider "aws" {
  allowed_account_ids = ["************"]
  profile             = "saml"
  region              = "us-east-1"
}
EOF
}

remote_state {
  backend = "s3"
  generate = {
    path      = "backend.tf"
    if_exists = "overwrite_terragrunt"
  }
  config = {
    bucket  = "i360-vandura-dev-fe-tf-state"
    key     = "${path_relative_to_include()}/terraform.tfstate"
    region  = "us-east-1"
    encrypt = true
    profile = "saml"
  }
}
