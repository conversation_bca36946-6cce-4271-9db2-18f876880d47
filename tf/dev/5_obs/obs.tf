terraform {
  required_providers {
    grafana = {
      source  = "grafana/grafana"
      version = "1.28.2"
    }
  }
}

provider "grafana" {
  url  = var.obs_config.grafana_url
  auth = var.obs_config.grafana_auth
}

locals {
  fe_scrape_configs = <<-EOF
  - job_name: system
    static_configs:
    - targets:
        - ${var.obs_config.grafana_public_ip}
      labels:
        job: varlogs
        __path__: /var/log/*log
  EOF
}

module "obs" {
  source                       = "**************-vnt:inv-cloud-platform/infra-com//tf/modules/obs?ref=v0.16.1"
  common_tags                  = var.common_tags
  obs_config                   = var.obs_config
  scrape_configs               = local.fe_scrape_configs
  grafana_dashboards_directory = "${path.module}/dashboards"
}

output "grafana_dashboards_folder_uid" {
  value = module.obs.grafana_dashboards_folder_uid
}

output "grafana_dashboard_uid" {
  value = module.obs.grafana_dashboard_uid
}
