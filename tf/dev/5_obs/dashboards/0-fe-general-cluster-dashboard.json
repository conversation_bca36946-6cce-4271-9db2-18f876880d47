{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "", "editable": true, "fiscalYearStartMonth": 0, "gnetId": 15759, "graphTooltip": 1, "id": 516, "links": [], "liveNow": false, "panels": [{"collapsed": false, "datasource": {"type": "prometheus", "uid": "prometheus"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 40, "panels": [], "title": "Overview", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"decimals": 2, "mappings": [], "max": 1, "min": 0, "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}, {"color": "orange", "value": 50}, {"color": "red", "value": 70}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 8, "w": 4, "x": 0, "y": 1}, "id": 7, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "text": {}}, "pluginVersion": "9.5.2", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "exemplar": false, "expr": "avg(1-rate(node_cpu_seconds_total{mode=\"idle\", instance=\"$instance\"}[$__rate_interval]))", "instant": true, "interval": "$resolution", "legendFormat": "", "refId": "A"}], "title": "CPU  Usage", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"decimals": 2, "mappings": [], "max": 1, "min": 0, "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}, {"color": "orange", "value": 50}, {"color": "red", "value": 70}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 8, "w": 4, "x": 4, "y": 1}, "id": 13, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "text": {}}, "pluginVersion": "9.5.2", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "exemplar": false, "expr": "sum(node_memory_MemTotal_bytes{instance=\"$instance\"} - node_memory_MemAvailable_bytes{instance=\"$instance\"}) / sum(node_memory_MemTotal_bytes{instance=\"$instance\"})", "instant": true, "interval": "$resolution", "legendFormat": "", "refId": "A"}], "title": "RAM Usage", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "blue", "value": null}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 4, "x": 8, "y": 1}, "id": 24, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "text": {}, "textMode": "value"}, "pluginVersion": "9.5.2", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "exemplar": true, "expr": "sum(kube_pod_info{node=\"$node\"})", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Pods on node", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "links": [{"targetBlank": true, "title": "Pod details", "url": "/d/k8s_views_pods/kubernetes-views-pods?${datasource:queryparam}&var-namespace=${__data.fields.namespace}&var-pod=${__data.fields.pod}&${resolution:queryparam}&${__url_time_range}"}], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "pod"}, "properties": [{"id": "custom.width", "value": 416}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "priority_class"}, "properties": [{"id": "custom.width", "value": 176}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "pod_ip"}, "properties": [{"id": "custom.width", "value": 157}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "created_by_kind"}, "properties": [{"id": "custom.width", "value": 205}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "namespace"}, "properties": [{"id": "custom.width", "value": 263}]}]}, "gridPos": {"h": 11, "w": 12, "x": 12, "y": 1}, "id": 5, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": false, "displayName": "namespace"}]}, "pluginVersion": "9.5.2", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "exemplar": true, "expr": "kube_pod_info{node=\"$node\"}", "format": "table", "interval": "", "legendFormat": "", "refId": "A"}], "title": "List of pods on node ($node)", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "Value": true, "__name__": true, "container": true, "created_by_kind": false, "created_by_name": true, "endpoint": true, "env": true, "host_ip": true, "host_network": true, "instance": true, "job": true, "node": true, "project": true, "prometheus_replica": true, "service": true, "uid": true}, "indexByName": {"Time": 6, "Value": 20, "__name__": 7, "container": 8, "created_by_kind": 2, "created_by_name": 9, "endpoint": 10, "env": 11, "host_ip": 5, "host_network": 12, "instance": 13, "job": 14, "namespace": 1, "node": 15, "pod": 0, "pod_ip": 3, "priority_class": 4, "project": 16, "prometheus_replica": 17, "service": 18, "uid": 19}, "renameByName": {}}}, {"id": "groupBy", "options": {"fields": {"created_by_kind": {"aggregations": [], "operation": "groupby"}, "host_ip": {"aggregations": [], "operation": "groupby"}, "namespace": {"aggregations": ["last"], "operation": "groupby"}, "pod": {"aggregations": [], "operation": "groupby"}, "pod_ip": {"aggregations": [], "operation": "groupby"}, "priority_class": {"aggregations": [], "operation": "groupby"}}}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"decimals": 3, "mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "rgb(255, 255, 255)", "value": null}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 2, "x": 0, "y": 9}, "id": 9, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.5.2", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "exemplar": false, "expr": "sum(1-rate(node_cpu_seconds_total{mode=\"idle\", instance=\"$instance\"}[$__rate_interval]))", "instant": true, "interval": "$resolution", "legendFormat": "", "refId": "A"}], "title": "CPU Used", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "rgb(255, 255, 255)", "value": null}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 2, "x": 2, "y": 9}, "id": 11, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.5.2", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "exemplar": true, "expr": "sum(machine_cpu_cores{node=\"$node\"})", "interval": "$resolution", "legendFormat": "", "refId": "A"}], "title": "CPU Total", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "rgb(255, 255, 255)", "value": null}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 3, "w": 2, "x": 4, "y": 9}, "id": 15, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.5.2", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "exemplar": false, "expr": "sum(node_memory_MemTotal_bytes{instance=\"$instance\"} - node_memory_MemAvailable_bytes{instance=\"$instance\"})", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "RAM Used", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "rgb(255, 255, 255)", "value": null}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 3, "w": 2, "x": 6, "y": 9}, "id": 17, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.5.2", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "exemplar": true, "expr": "machine_memory_bytes{node=\"$node\"}", "instant": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "RAM Total", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 25228800}, {"color": "red", "value": 31536000}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 3, "w": 4, "x": 8, "y": 9}, "id": 18, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.5.2", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "exemplar": true, "expr": "node_time_seconds{instance=\"$instance\"}", "instant": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "uptime", "type": "stat"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "prometheus"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 12}, "id": 38, "panels": [], "title": "Resources", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "CPU Cores", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 25, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 13}, "id": 26, "options": {"legend": {"calcs": [], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "exemplar": true, "expr": "sum(rate(container_cpu_usage_seconds_total{node=\"$node\", image!=\"\"}[$__rate_interval])) by (pod)", "interval": "$resolution", "legendFormat": "{{ pod }}", "refId": "A"}], "title": "CPU usage by Pod", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 25, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 13}, "id": 28, "options": {"legend": {"calcs": [], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "exemplar": true, "expr": "sum(container_memory_working_set_bytes{node=\"$node\", image!=\"\"}) by (pod)", "interval": "$resolution", "legendFormat": "{{ pod }}", "refId": "A"}], "title": "Memory usage by Pod", "type": "timeseries"}], "refresh": "30s", "schemaVersion": 38, "style": "dark", "tags": ["Kubernetes", "Prometheus"], "templating": {"list": [{"current": {"selected": false, "text": "api-proxy-dev", "value": "api-proxy-dev"}, "datasource": {"type": "prometheus", "uid": "${datasource}"}, "definition": "label_values(node_uname_info, job)", "hide": 2, "includeAll": true, "multi": true, "name": "job", "options": [], "query": {"query": "label_values(node_uname_info, job)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "type": "query"}, {"current": {"selected": false, "text": "Prometheus - Development", "value": "Prometheus - Development"}, "hide": 0, "includeAll": true, "multi": true, "name": "datasource", "options": [], "query": "prometheus", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"current": {"selected": true, "text": "30s", "value": "30s"}, "hide": 0, "includeAll": true, "multi": true, "name": "resolution", "options": [{"selected": false, "text": "1s", "value": "1s"}, {"selected": false, "text": "15s", "value": "15s"}, {"selected": true, "text": "30s", "value": "30s"}, {"selected": false, "text": "1m", "value": "1m"}, {"selected": false, "text": "3m", "value": "3m"}, {"selected": false, "text": "5m", "value": "5m"}], "query": "1s, 15s, 30s, 1m, 3m, 5m", "queryValue": "", "skipUrlSync": false, "type": "custom"}, {"current": {"selected": false, "text": "ip-10-112-23-207.ec2.internal", "value": "ip-10-112-23-207.ec2.internal"}, "datasource": {"type": "prometheus", "uid": "${datasource}"}, "definition": "label_values(kube_node_info, node)", "hide": 0, "includeAll": true, "multi": true, "name": "node", "options": [], "query": {"query": "label_values(kube_node_info, node)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "(ip-10-112-20-224.ec2.internal.*|ip-10-112-23-207.ec2.internal.*)", "skipUrlSync": false, "sort": 1, "type": "query"}, {"current": {"isNone": true, "selected": false, "text": "None", "value": ""}, "datasource": {"type": "prometheus", "uid": "${datasource}"}, "definition": "label_values(node_uname_info{nodename=~\"(?i:($node))\"}, instance)", "hide": 2, "includeAll": true, "multi": true, "name": "instance", "options": [], "query": {"query": "label_values(node_uname_info{nodename=~\"(?i:($node))\"}, instance)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 1, "type": "query"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "fe-cluster-metrics", "uid": "k8s_views_nodes_fe", "version": 22, "weekStart": ""}