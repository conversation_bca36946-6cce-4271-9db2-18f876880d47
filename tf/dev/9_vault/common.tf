variable "common_tags" { // need to rerun all modules to apply any changes
  type = map(string)
  default = {
    env          = "dev"
    region       = "us"
    subsystem    = "fe"
    creator      = "fe-infra"
    product-line = "Infrastructure" // to match global IS360 tag
    architecture = "vandura"
  }
}

variable "repo_names" { // need to rerun 1_base,  2_eks, 3_eks_setup to apply any changes
  type = set(string)
  default = [
    // fe-am
    "fe-am-e2e",
    "fe-am-playwright",
    "fe-am-ui-bff",
    "fe-am-ui-devices",
    "fe-am-ui-sites",
    "fe-am-ui-filters-api",
    // fe-fm
    "fe-fm-e2e",
    "fe-fm-playwright",
    "fe-fm-ui-bff",
    "fe-fm-ui-logistics",
    "fe-fm-ui-inventory",
    "fe-fm-usage-api",
    "fe-fm-mongo-setup",
    // fe-wsm
    "fe-wsm-ui",
    "fe-wsm-bff",
    "fe-wsm-e2e",
    "fe-wsm-playwright",
    // fe-wm
    "fe-wm-e2e",
    "fe-wm-playwright",
    "fe-wm-ui",
    "fe-wm-bff",
    // fe-tlstech
    "fe-tlstech-ui",
    "fe-tlstech-bff",
    "fe-tlstech-e2e",
    // fe-ev
    "fe-ev-csms-api",
    "fe-ev-csms-io",
    "fe-ev-location-api",
    "fe-ev-location-in",
    "fe-ev-session-api",
    "fe-ev-session-in",
    "fe-ev-obj-update-io",
    "fe-ev-cdr-api",
    "fe-ev-cdr-in",
    "fe-ev-tariff-api",
    "fe-ev-tariff-in",
    "fe-ev-finadv-io",
    "fe-ev-e2e",
    "fe-ev-rcpt-tmpl-api",
    "fe-ev-rcpt-api",
    "fe-ev-opt-evse-api",
    "fe-ev-rcpt-tmpl-bff",
    "fe-ev-smsrcpt-io",
    "fe-ev-opt-bff",
    "fe-ev-e2e-twk",

    // fe-ucc
    "fe-ucc-cli-bff",
    // fe-swum
    "fe-swum-ui-bff",
    "fe-swum-ui-assets",
    // fe-devapp
    "fe-devapp-ui",
    // fe-ev-rcpt-tmpl
    "fe-ev-rcpt-tmpl-ui",
    "fe-ppe-deployments-ui",
    "fe-ppe-status-bff",
    "fe-ev-opt-ui",
    "fe-ppe-logs-api",
    "fe-wsm-iso-trans-api",
    // fe-pcn
    "fe-pcn-ui",
    "fe-pcn-bff",
    // fe-dispctlr
    "fe-dispctlr-ui",
    "fe-dispctlr-bff"
  ]
}

variable "apps" { // we'll create namespaces for each app
  type = set(string)
  default = [
    "am",
    "wm",
    "fm",
    "cdm",
    "wsm",
    "ev",
    "tlstech",
    "devapp",
    "ucc",
    "swum",
    "ppe",
    "pcn",
    "dispctlr"
  ]
}

variable "prod_account_number" { // used to create policies that allow prod to read ECR repos
  type    = string
  default = "************"
}

variable "region" {
  type    = string
  default = "us-east-1"
}

variable "monitoring_service_type" {
  type    = string
  default = "NodePort"
}

### Prometheus variables
variable "prometheus" {
  type = map(string)
  default = {
    datasource_uid = "VPm26Zf7z"
  }
}

### common obs config
variable "obs_config" {
  type = map(string)
  default = {
    amp_remote_write_url  = "https://aps-workspaces.us-east-1.amazonaws.com/workspaces/ws-c3f49063-39d6-4e3e-bcbf-d5ca950782f3/api/v1/remote_write"
    grafana_auth          = "glsa_EzxPnwSTxY2EgSjfNYg9SFwZxSaLinCh_ab924002"
    grafana_url           = "https://grafana.qa.is360.io/"
    contact_point_webhook = "https://vontier.webhook.office.com/webhookb2/ab147851-b184-4732-857c-80dd82c46cea@8df22a8d-9153-489d-950c-a20cccb65620/IncomingWebhook/bdbf4420086b4eb384f8279d64dd7a44/afca3537-63c3-4847-aa54-edf4e4c0f93b/V26LIJHgTZ9ffHeXflth39MUK8XTSofRYtHDQnsyH7isk1"
    loki_push_url         = "http://grafana.qa.is360.io:3100/loki/api/v1/push"
    grafana_public_ip     = "grafana.qa.is360.io"
    grafana_team          = "Connectar"
  }
}

### Boundary and Vault variables
variable "vault_boundary_config" {
  type = map(string)
  default = {
    vault_url         = "https://vault.dev.is360.io:8200"
    vault_role_name   = "kubernetes-dev-fe"
    vault_policy_name = "dev" //vault developer policy
    vault_auth_mount  = "kubernetes-dev-fe"
    fe_secrets_name   = "fe-secrets"
    am_secrets_name   = "fe-am-secrets"
    fm_secrets_name   = "fe-fm-secrets"
    wm_secrets_name   = "fe-wm-secrets"
    ev_secrets_name   = "fe-ev-secrets"
  }
}

variable "mongo_db_name" {
  type    = string
  default = "dev-mongodbatlas"
}

variable "vault_mongo_public_key" {
  type = string
  default = "fmhbblxd"
}
