terraform {
  required_providers {
    kubectl = {
      source  = "gavi<PERSON><PERSON><PERSON>/kubectl"
      version = ">= 1.14.0"
    }
  }
}

provider "kubectl" {
  host                   = local.kubernetes_host
  cluster_ca_certificate = local.kubernetes_ca_cert
  token                  = data.aws_eks_cluster_auth.cluster.token
}

provider "kubernetes" {
  host                   = local.kubernetes_host
  cluster_ca_certificate = local.kubernetes_ca_cert
  token                  = data.aws_eks_cluster_auth.cluster.token
}

data "aws_eks_cluster" "cluster" {
  name = local.kubernetes_cluster
}
 
data "aws_eks_cluster_auth" "cluster" {
  name = local.kubernetes_cluster
}

locals {
  kubernetes_cluster  = "${var.common_tags.env}-${var.common_tags.subsystem}"
  kubernetes_host     = data.aws_eks_cluster.cluster.endpoint
  kubernetes_ca_cert  = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
}

module "vault-boundary-k8s-auth" {
  source                  = "**************-vnt:inv-cloud-platform/infra-boundary//deployment/aws/modules/vault_boundary_k8s_auth?ref=v0.0.3"
  
  vault_boundary_config   = var.vault_boundary_config
  kubernetes_cluster      = local.kubernetes_cluster
  kubernetes_host         = local.kubernetes_host
  kubernetes_ca_cert      = local.kubernetes_ca_cert
}

module "vault-secrets-operator" {
  source                  = "**************-vnt:inv-cloud-platform/infra-com//tf/modules/vault_secrets_operator?ref=v0.16.1"
  
  common_tags             = var.common_tags
  vault_boundary_config   = var.vault_boundary_config
  contact_point_webhook   = var.obs_config.contact_point_webhook
  dashboard_url           = "https://g-c5ffaf5d52.grafana-workspace.us-east-1.amazonaws.com/d/k4HXVjP4z/vault-sync-operator-dashboard?orgId=1&refresh=10s"
}

resource "kubectl_manifest" "vault_static_secret_fe" {
  yaml_body = <<EOF
apiVersion: secrets.hashicorp.com/v1beta1
kind: VaultStaticSecret
metadata:
  name: ${var.vault_boundary_config.fe_secrets_name}
  namespace: "${var.common_tags.subsystem}"
spec:
  mount: ${var.common_tags.env}
  path: "${var.common_tags.env}-${var.common_tags.subsystem}"
  type: kv-v2
  refreshAfter: 10s
  destination:
    name: ${var.vault_boundary_config.fe_secrets_name}
    create: true
EOF

  depends_on = [
    module.vault-boundary-k8s-auth,
    module.vault-secrets-operator
  ]
}

resource "kubectl_manifest" "vault_static_secret_fe_fm" {
  yaml_body = <<EOF
apiVersion: secrets.hashicorp.com/v1beta1
kind: VaultStaticSecret
metadata:
  name: ${var.vault_boundary_config.fm_secrets_name}
  namespace: "${var.common_tags.subsystem}-fm"
spec:
  mount: ${var.common_tags.env}
  path: "${var.common_tags.env}-${var.common_tags.subsystem}-fm"
  type: kv-v2
  refreshAfter: 10s
  destination:
    name: ${var.vault_boundary_config.fm_secrets_name}
    create: true
EOF

  depends_on = [
    module.vault-boundary-k8s-auth,
    module.vault-secrets-operator
  ]
}

resource "kubectl_manifest" "vault_static_secret_fe_am" {
  yaml_body = <<EOF
apiVersion: secrets.hashicorp.com/v1beta1
kind: VaultStaticSecret
metadata:
  name: ${var.vault_boundary_config.am_secrets_name}
  namespace: "${var.common_tags.subsystem}-am"
spec:
  mount: ${var.common_tags.env}
  path: "${var.common_tags.env}-${var.common_tags.subsystem}-am"
  type: kv-v2
  refreshAfter: 10s
  destination:
    name: ${var.vault_boundary_config.am_secrets_name}
    create: true
EOF

  depends_on = [
    module.vault-boundary-k8s-auth,
    module.vault-secrets-operator
  ]
}

resource "kubectl_manifest" "vault_static_secret_fe_wm" {
  yaml_body = <<EOF
apiVersion: secrets.hashicorp.com/v1beta1
kind: VaultStaticSecret
metadata:
  name: ${var.vault_boundary_config.wm_secrets_name}
  namespace: "${var.common_tags.subsystem}-wm"
spec:
  mount: ${var.common_tags.env}
  path: "${var.common_tags.env}-${var.common_tags.subsystem}-wm"
  type: kv-v2
  refreshAfter: 10s
  destination:
    name: ${var.vault_boundary_config.wm_secrets_name}
    create: true
EOF

  depends_on = [
    module.vault-boundary-k8s-auth,
    module.vault-secrets-operator
  ]
}

resource "kubectl_manifest" "vault_static_secret_fe_ev" {
  yaml_body = <<EOF
apiVersion: secrets.hashicorp.com/v1beta1
kind: VaultStaticSecret
metadata:
  name: ${var.vault_boundary_config.ev_secrets_name}
  namespace: "${var.common_tags.subsystem}-ev"
spec:
  mount: ${var.common_tags.env}
  path: "${var.common_tags.env}-${var.common_tags.subsystem}-ev"
  type: kv-v2
  refreshAfter: 10s
  destination:
    name: ${var.vault_boundary_config.ev_secrets_name}
    create: true
EOF

  depends_on = [
    module.vault-boundary-k8s-auth,
    module.vault-secrets-operator
  ]
}
