terraform {
  required_providers {
    vault = {
      # Environment variables expected $VAULT_ADDR and $VAULT_TOKEN
      source  = "hashicorp/vault"
      version = "3.17.0"
    }
    mongodbatlas = {
      source  = "mongodb/mongodbatlas"
      version = "1.21.4"
    }
  }
}

provider "vault" {
  address = var.vault_boundary_config.vault_url
}

variable "DEFAULT_TTL" {
  description = "Default value for all TTLs"
  default     = 86400 // 24h
}

variable "MAXIMUM_TTL" {
  description = "Maximum value for all TTLs"
  default     = 86400 // 24h
}

locals {
  version = "v1.0.0"

  mongo = {
    projects = {
      "${var.common_tags.env}-${var.common_tags.subsystem}-ev" = {
        roles = {
          // Remove after migration to the below one
          "dev-fe-ev-csms-api" = {
            creation_statements = jsonencode({ "database_name" : "admin", "roles" : [
              { "databaseName" : "fe-ev-secure", "roleName" : "readWrite", "collectionName" : "connections" },
              { "databaseName" : "fe-ev-common", "roleName" : "read", "collectionName" : "locations" }
            ] }),
          },
          "fe-ev-csms-api" = {
            creation_statements = jsonencode({ "database_name" : "admin", "roles" : [
              { "databaseName" : "fe-ev-secure", "roleName" : "readWrite", "collectionName" : "connections" },
              { "databaseName" : "fe-ev-common", "roleName" : "read", "collectionName" : "locations" }
            ] }),
          },
          "fe-ev-location-in" = {
            creation_statements = jsonencode({ "database_name" : "admin", "roles" : [{ "databaseName" : "fe-ev-common", "roleName" : "readWrite", "collectionName" : "locations" }] })
          },
          "fe-ev-location-api" = {
            creation_statements = jsonencode({ "database_name" : "admin", "roles" : [{ "databaseName" : "fe-ev-common", "roleName" : "read", "collectionName" : "locations" }] })
          },
          "fe-ev-session-in" = {
            creation_statements = jsonencode({ "database_name" : "admin", "roles" : [{ "databaseName" : "fe-ev-common", "roleName" : "readWrite", "collectionName" : "sessions" }] })
          }
          "fe-ev-session-api" = {
            creation_statements = jsonencode({ "database_name" : "admin", "roles" : [{ "databaseName" : "fe-ev-common", "roleName" : "read", "collectionName" : "sessions" }] })
          },
          "fe-ev-cdr-in" = {
            creation_statements = jsonencode({ "database_name" : "admin", "roles" : [{ "databaseName" : "fe-ev-common", "roleName" : "readWrite", "collectionName" : "cdrs" }] })
          },
          "fe-ev-cdr-api" = {
            creation_statements = jsonencode({ "database_name" : "admin", "roles" : [{ "databaseName" : "fe-ev-common", "roleName" : "read", "collectionName" : "cdrs" }] })
          },
          "fe-ev-tariff-in" = {
            creation_statements = jsonencode({ "database_name" : "admin", "roles" : [{ "databaseName" : "fe-ev-common", "roleName" : "readWrite", "collectionName" : "tariffs" }] })
          },
          "fe-ev-tariff-api" = {
            creation_statements = jsonencode({ "database_name" : "admin", "roles" : [{ "databaseName" : "fe-ev-common", "roleName" : "read", "collectionName" : "tariffs" }] })
          },
          "fe-ev-csms-io" = {
            creation_statements = jsonencode({ "database_name" : "admin", "roles" : [{ "databaseName" : "fe-ev-secure", "roleName" : "read", "collectionName" : "connections" },] })
          },
          "fe-ev-rcpt-tmpl-api" = {
            creation_statements = jsonencode({ "database_name" : "admin", "roles" : [{ "databaseName" : "fe-ev-common", "roleName" : "readWrite", "collectionName" : "templates" }] })
          },
          "fe-ev-finadv-io" = {
            creation_statements = jsonencode({ "database_name" : "admin", "roles" : [{ "databaseName" : "fe-ev-common", "roleName" : "readWrite", "collectionName" : "receipts" }] })
          },
          "fe-ev-rcpt-api" = {
            creation_statements = jsonencode({ "database_name" : "admin", "roles" : [{ "databaseName" : "fe-ev-common", "roleName" : "readWrite", "collectionName" : "receipts" }] })
          },
          "fe-ev-opt-evse-api" = {
            creation_statements = jsonencode({ "database_name" : "admin", "roles" : [{ "databaseName" : "fe-ev-common", "roleName" : "readWrite", "collectionName" : "evses" }] })
          },
          "fe-ev-rcpt-tmpl-bff" = {
            creation_statements = jsonencode({ "database_name" : "admin", "roles" : [{ "databaseName" : "fe-ev-secure", "roleName" : "read", "collectionName" : "connections" }] })
          },
          "fe-ev-opt-bff" = {
            creation_statements = jsonencode({ "database_name" : "admin", "roles" : [{ "databaseName" : "fe-ev-secure", "roleName" : "read", "collectionName" : "connections" }] })
          },
          "fe-ppe-logs-api" = {
            creation_statements = jsonencode({ "database_name" : "admin", "roles" : [{ "databaseName" : "passport_enterprise", "roleName" : "readWrite", "collectionName" : "logs_request" }] })
          },
          # add new role here..
        }
      },
      "${var.common_tags.env}-${var.common_tags.subsystem}-wsm" = {
        roles = {
          "fe-wsm-iso-trans-in" = {
            creation_statements = jsonencode({ "database_name" : "admin", "roles" : [{ "databaseName" : "analytics", "roleName" : "readWrite", "collectionName" : "isolated_transaction " }] })
          }
          "fe-wsm-iso-trans-api" = {
            creation_statements = jsonencode({ "database_name" : "admin", "roles" : [{ "databaseName" : "analytics", "roleName" : "read", "collectionName" : "isolated_transaction " }] })
          },
          # add new wsm roles here..
        }
      }
      # add new project here
    }
  }

}

locals {
  project_roles = flatten([
    for project_name, project in local.mongo.projects : [
      for role_name, role_data in project.roles : {
        project_name        = project_name
        role_name           = role_name
        creation_statements = role_data.creation_statements
        ttl                 = lookup(role_data, "ttl", var.DEFAULT_TTL)
      }
    ]
  ])
}

data "mongodbatlas_project" "name" {
  for_each = local.mongo.projects
  name     = each.key
}

resource "vault_database_secret_backend_connection" "mongodb" {
  for_each = data.mongodbatlas_project.name
  backend  = "database/${var.mongo_db_name}"
  name     = each.value.name
  allowed_roles = [
    "fe-am-*",
    "fe-ev-*", "dev-fe-ev-*",
    "fe-fm-*",
    "fe-wm-*",
    "fe-wsm-*",
  ]

  # DB connection initiated in TF with foo credentials.
  # Then set manually to keep keys out of tfstate.e.g
  /*
  vault write database/dev-mongodbatlas/config/dev-mongodbatlas \ 
    plugin_name=mongodbatlas-database-plugin \
    public_key="*" \
    private_key="*" \
    project_id="*"
    */
  mongodbatlas {
    public_key  = var.vault_mongo_public_key
    private_key = "setMeViaCLI"
    project_id  = each.value.project_id
  }

  # ignore manual changes to keys
  lifecycle {
    ignore_changes = [mongodbatlas]
  }

}

resource "vault_database_secret_backend_role" "mongo_role" {
  for_each = { for pr in local.project_roles : "${pr.role_name}" => pr }

  backend = "database/${var.mongo_db_name}"
  name    = each.value.role_name
  db_name = each.value.project_name

  creation_statements = [each.value.creation_statements]

  default_ttl = each.value.ttl
  max_ttl     = var.MAXIMUM_TTL
}
