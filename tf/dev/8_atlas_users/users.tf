terraform {
  required_providers {
    mongodbatlas = {
      source  = "mongodb/mongodbatlas"
      version = ">= 1.15"
    }
  }
}

# assumes MONGODB_ATLAS_PUBLIC_KEY and MONGODB_ATLAS_PRIVATE_KEY are set in env
provider "mongodbatlas" {
}

data "mongodbatlas_project" "project" {
  name = "${var.common_tags.env}-${var.common_tags.subsystem}"
}

data "mongodbatlas_project" "ev" {
  name = "${var.common_tags.env}-${var.common_tags.subsystem}-ev"
}

variable "fe_fm_usage_api_password" { sensitive = true }
resource "mongodbatlas_database_user" "fe_fm_usage_api" {
  username           = "fe-fm-usage-api"
  password           = var.fe_fm_usage_api_password
  project_id         = data.mongodbatlas_project.project.id
  auth_database_name = "admin"

  roles {
    role_name     = "readWrite"
    database_name = "ifm"
  }
}

data "mongodbatlas_project" "cdm" {
  name = "${var.common_tags.env}-${var.common_tags.subsystem}-cdm"
}
variable "fe_cdm_admin_atlas_password" { sensitive = true }
resource "mongodbatlas_database_user" "fe_cdm_admin" {
  username           = "fe-cdm-admin"
  password           = var.fe_cdm_admin_atlas_password
  project_id         = data.mongodbatlas_project.cdm.id
  auth_database_name = "admin"

  roles {
    role_name     = "dbAdminAnyDatabase"
    database_name = "admin"
  }
  roles {
    role_name     = "readWrite"
    database_name = "cdm"
  }
}

variable "fe_fm_cdc_api_password" { sensitive = true }
resource "mongodbatlas_database_user" "fe_fm_cdc_api" {
  username           = "fe-fm-cdc-api"
  password           = var.fe_fm_cdc_api_password
  project_id         = data.mongodbatlas_project.project.id
  auth_database_name = "admin"

  roles {
    role_name     = "readWrite"
    database_name = "ifm"
  }
}

variable "fe_am_ui_filters_api_password" { sensitive = true }
resource "mongodbatlas_database_user" "fe_am_ui_filters_api" {
  username           = "fe-am-ui-filters-api"
  password           = var.fe_am_ui_filters_api_password
  project_id         = data.mongodbatlas_project.project.id
  auth_database_name = "admin"

  roles {
    role_name     = "readWrite"
    database_name = "am"
  }
}

variable "fe_fm_mongo_setup_password" { sensitive = true }
resource "mongodbatlas_database_user" "fe_fm_mongo_setup" {
  username           = "fe-fm-mongo-setup"
  password           = var.fe_fm_mongo_setup_password
  project_id         = data.mongodbatlas_project.project.id
  auth_database_name = "admin"

  roles {
    role_name     = "readWrite"
    database_name = "ifm"
  }
}

variable "fe_ev_csms_api_password" { sensitive = true }
resource "mongodbatlas_database_user" "fe_ev_csms_api" {
  username           = "fe_ev_csms_api"
  password           = var.fe_ev_csms_api_password
  project_id         = data.mongodbatlas_project.ev.id
  auth_database_name = "admin"

  roles {
    role_name     = "readWrite"
    database_name = "fe-ev-secure"
  }
}
