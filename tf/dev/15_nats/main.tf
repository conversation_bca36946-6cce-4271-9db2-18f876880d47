provider "helm" {
  kubernetes {
    host                   = data.aws_eks_cluster.cluster.endpoint
    cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
    token                  = data.aws_eks_cluster_auth.cluster.token
  }
}

provider "kubectl" {
  host                   = data.aws_eks_cluster.cluster.endpoint
  cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
  token                  = data.aws_eks_cluster_auth.cluster.token
}

provider "grafana" {
  url  = var.obs_config.grafana_url
  auth = var.obs_config.grafana_auth
}

data "aws_eks_cluster" "cluster" {
  name = "${var.common_tags.env}-${var.common_tags.subsystem}"
}

data "aws_eks_cluster_auth" "cluster" {
  name = "${var.common_tags.env}-${var.common_tags.subsystem}"
}

module "nats" {
  source = "**************:inv-cloud-platform/infra-com//tf/modules/nats?ref=v0.14.1"

  prometheus_datasourceid   = var.prometheus.datasource_uid
  deploy_grafana_dashboards = false
  namespace                 = var.common_tags.subsystem
  nats_url                  = "nats://nats.${var.common_tags.subsystem}.svc.cluster.local:4222"
  nats_additional_values    = "additional_values.yml"
}

