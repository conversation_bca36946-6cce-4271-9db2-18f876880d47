config:
  gateway:
    enabled: true
    port: 7222
    connect_retries: 1
    merge:
      gateways:
        - name: nats-ia
          url: "nats://nats.ia.svc.cluster.local:7222"
        - name: nats-cp
          url: "nats://nats.cp.svc.cluster.local:7222"
        - name: nats-dp
          url: "nats://nats.dp.svc.cluster.local:7222"
        - name: nats-fe
          url: "nats://nats.fe.svc.cluster.local:7222"
        

statefulSet:
  name: nats-fe

service:
  ports:
    gateway:
      enabled: true
  patch:
    - op: add
      path: "/metadata/annotations"
      value:
        "io.cilium/global-service": "true"
        "io.cilium/shared-service": "true"
