resource "aws_ecr_repository" "repo" {
  for_each = var.repo_names

  name                 = each.key
  image_tag_mutability = "IMMUTABLE"
  force_delete         = true // only for dev

  image_scanning_configuration {
    scan_on_push = false // do this at a higher level
  }

  tags = var.common_tags
}

// See: https://aws.amazon.com/premiumsupport/knowledge-center/secondary-account-access-ecr/
resource "aws_ecr_repository_policy" "ecr_repo_policies" { // Based on AmazonEC2ContainerRegistryReadOnly
  for_each   = var.repo_names
  repository = aws_ecr_repository.repo[each.key].name

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "ecr:GetAuthorizationToken",
          "ecr:BatchCheckLayerAvailability",
          "ecr:GetDownloadUrlForLayer",
          "ecr:GetRepositoryPolicy",
          "ecr:DescribeRepositories",
          "ecr:ListImages",
          "ecr:DescribeImages",
          "ecr:BatchGetImage",
          "ecr:GetLifecyclePolicy",
          "ecr:GetLifecyclePolicyPreview",
          "ecr:ListTagsForResource",
          "ecr:DescribeImageScanFindings"
        ]
        Effect = "Allow"
        Principal = {
          "AWS" : "arn:aws:iam::${var.prod_account_number}:root"
        }
        Sid = "ProdRo"
      }
    ]
  })
}
