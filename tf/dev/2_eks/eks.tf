data "aws_caller_identity" "current" {}

data "aws_region" "current" {}

data "aws_ecr_repository" "repo" {
  for_each = var.repo_names
  name     = each.key
}

data "aws_ecr_repository" "runner_repo" {
  for_each = toset(local.runner_repos)
  name     = each.key
}

locals {
  runner_repos = [
    "infra-arcimage-base"
  ]
  repo_arns = [
    for v in var.repo_names :
    data.aws_ecr_repository.repo[v].arn
  ]
  runner_repo_arns = [
    for v in local.runner_repos :
    data.aws_ecr_repository.runner_repo[v].arn
  ]
}

resource "aws_iam_policy" "full_ecr_access" {
  name        = "${var.common_tags.subsystem}.ecr.full"
  description = "Full access to ECR repos"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "ecr:GetAuthorizationToken",
          "ecr:BatchCheckLayerAvailability",
          "ecr:GetDownloadUrlForLayer",
          "ecr:GetRepositoryPolicy",
          "ecr:DescribeRepositories",
          "ecr:ListImages",
          "ecr:DescribeImages",
          "ecr:BatchGetImage",
          "ecr:InitiateLayerUpload",
          "ecr:UploadLayerPart",
          "ecr:CompleteLayerUpload",
        "ecr:PutImage"]
        Effect   = "Allow"
        Resource = local.repo_arns
      },
    ]
  })
  tags = var.common_tags
}

resource "aws_iam_policy" "arcimage_ro" {
  name        = "${var.common_tags.subsystem}.ecr.arcimagero"
  description = "Access to ARC Build Images"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "ecr:GetAuthorizationToken",
          "ecr:BatchCheckLayerAvailability",
          "ecr:GetDownloadUrlForLayer",
          "ecr:GetRepositoryPolicy",
          "ecr:DescribeRepositories",
          "ecr:ListImages",
          "ecr:DescribeImages",
          "ecr:BatchGetImage",
          "ecr:GetLifecyclePolicy",
          "ecr:GetLifecyclePolicyPreview",
          "ecr:ListTagsForResource",
          "ecr:DescribeImageScanFindings"
        ]
        Effect   = "Allow"
        Resource = local.runner_repo_arns
      },
    ]
  })
  tags = var.common_tags
}

module "eks" {
  source = "**************-vnt:inv-cloud-platform/infra-com//tf/modules/eks?ref=v0.20.3"

  common_tags               = var.common_tags
  aws_lb_controller_enabled = true
  keda_enabled              = true
  # keda_iam_policies         = ["arn:aws:iam::aws:policy/AmazonSQSFullAccess"]

  iam_role_additional_policies = {
    full_ecr_access = aws_iam_policy.full_ecr_access.arn,
    arcimage_ro     = aws_iam_policy.arcimage_ro.arn,
  }

  disk_size     = 50
  instance_type = "m5a.large"
  desired_size  = 5
  min_size      = 4
  max_size      = 6

  permissions = [
    {
      name               = "dev",
      verbs              = "['get', 'watch', 'list']"
      resources          = "['pods', 'pods/log', 'services', 'secrets']"
      apps_verbs         = "['get', 'watch', 'list']"
      apps_resources     = "['deployments','replicasets']"
      enable_portforward = true
    },
    {
      name               = "lead",
      verbs              = "['get', 'watch', 'list']"
      resources          = "['pods', 'pods/log', 'services', 'secrets']"
      apps_verbs         = "['get', 'watch', 'list']"
      apps_resources     = "['deployments', 'replicasets']"
      enable_portforward = true
    },
  ]

  aws_auth_roles = [
    {
      rolearn  = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/sso.i360.io-full-admin"
      username = "sso.i360.io-full-admin"
      groups   = ["system:masters"]
    },
    { # enable developers to admin cluster in dev environment, do not use in prod
      rolearn  = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/sso.i360.io-admin-power"
      username = "sso.i360.io-admin-power"
      groups   = ["system:masters"]
    },
    {
      rolearn  = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/sso.i360.io-fe-cdm-admin"
      username = "sso.i360.io-fe-cdm-admin"
      groups   = ["admin"]
    },
    {
      rolearn  = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/sso.i360.io-fe-cdm-pipeline"
      username = "sso.i360.io-fe-cdm-pipeline"
      groups   = ["admin"]
    },
  ]

  aws_auth_users = [
    {
      userarn  = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:user/<EMAIL>"
      username = "<EMAIL>"
      groups   = ["system:masters"]
    }
  ]

  prom_remote_write_url = "https://aps-workspaces.us-east-1.amazonaws.com/workspaces/ws-c3f49063-39d6-4e3e-bcbf-d5ca950782f3/api/v1/remote_write"
  
  teams_webhook_url = var.obs_config.contact_point_webhook
}
