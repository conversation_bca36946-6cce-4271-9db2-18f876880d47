provider "kubernetes" {
  host                   = data.aws_eks_cluster.cluster.endpoint
  cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
  token                  = data.aws_eks_cluster_auth.cluster.token
}

data "aws_eks_cluster" "cluster" {
  name = "${var.common_tags.env}-${var.common_tags.subsystem}"
}

data "aws_eks_cluster_auth" "cluster" {
  name = "${var.common_tags.env}-${var.common_tags.subsystem}"
}

module "arc_org" {
  source = "**************-vnt:inv-cloud-platform/infra-com//tf/modules/arc_org?ref=v0.12.3"

  github_app_id              = "643781"
  github_app_installation_id = "44276989"
  github_app_private_key     = file("${path.module}/vnt-dev-fe-arc.private-key.pem")
  runner_image               = "633377509572.dkr.ecr.us-east-1.amazonaws.com/infra-arcimage-base:latest"

  common_tags = var.common_tags
}
