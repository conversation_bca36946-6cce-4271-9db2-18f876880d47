resource "mongodbatlas_project" "cdm" {
  name                         = "${var.common_tags.env}-${var.common_tags.subsystem}-cdm"
  org_id                       = local.atlas_org_id
  with_default_alerts_settings = true

  is_collect_database_specifics_statistics_enabled = true
  is_data_explorer_enabled                         = true
  is_performance_advisor_enabled                   = true
  is_schema_advisor_enabled                        = true
}

resource "mongodbatlas_cluster" "cdm" {
  project_id   = mongodbatlas_project.cdm.id
  name         = "${var.common_tags.env}-${var.common_tags.subsystem}-cdm"
  cluster_type = "REPLICASET"

  replication_specs {
    num_shards = 1
    regions_config {
      region_name     = "US_EAST_1"
      electable_nodes = 3
      priority        = 7
      read_only_nodes = 0
    }
  }

  cloud_backup                 = true
  auto_scaling_disk_gb_enabled = true
  auto_scaling_compute_enabled = true
  mongo_db_major_version       = "7.0"
  version_release_system       = "LTS"

  # Provider Settings "block"
  provider_name                                   = "AWS"
  disk_size_gb                                    = 100
  provider_instance_size_name                     = "M10"
  provider_auto_scaling_compute_min_instance_size = "M10"
  provider_auto_scaling_compute_max_instance_size = "M20"

  lifecycle { ignore_changes = [provider_instance_size_name] }
}

resource "mongodbatlas_privatelink_endpoint" "cdm" {
  project_id    = mongodbatlas_project.cdm.id
  provider_name = "AWS"
  region        = "US_EAST_1"
}

resource "aws_vpc_endpoint" "cdm" {
  vpc_id       = data.aws_vpc.vpc.id
  service_name = mongodbatlas_privatelink_endpoint.cdm.endpoint_service_name
  auto_accept  = true

  vpc_endpoint_type = "Interface"

  # can't be turned on until after the acceptance
  # private_dns_enabled = true

  subnet_ids         = data.aws_subnets.private_ids.ids
  security_group_ids = [aws_security_group.allow_all_vpc.id]

  tags = merge(
    var.common_tags,
    { "Name" = "${var.common_tags.env}-van-${var.common_tags.subsystem}-cdm-atlas" }
  )
}

resource "mongodbatlas_privatelink_endpoint_service" "cdm_atlas_to_subsystem" {
  project_id          = mongodbatlas_privatelink_endpoint.cdm.project_id
  private_link_id     = mongodbatlas_privatelink_endpoint.cdm.private_link_id
  endpoint_service_id = aws_vpc_endpoint.cdm.id
  provider_name       = "AWS"
}
