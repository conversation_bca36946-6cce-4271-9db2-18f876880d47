NOTE: Due to race conditions between AWS and atlas that could result in confused state, if you get the following error:

```
mongodbatlas_cluster.fm: Creation complete after 8m42s [id=Y2x1c3Rlcl9pZA==:NjVkY2Y3YjM3YmY2OGIwZDFmYWUzN2Vl-Y2x1c3Rlcl9uYW1l:cHJvZC1mZS1mbQ==-cHJvamVjdF9pZA==:NjVkY2Y3YjFiZDc5ZDg2ZDE2MTBiY2Zk-cHJvdmlkZXJfbmFtZQ==:QVdT]
╷
│ Error: reading EC2 VPC Endpoint Service Configuration (com.amazonaws.vpce.us-east-1.vpce-svc-0a1404d72439cc214): empty result
│
│   with aws_vpc_endpoint.ptfe_service,
│   on private_link.tf line 50, in resource "aws_vpc_endpoint" "ptfe_service":
│   50: resource "aws_vpc_endpoint" "ptfe_service" {
│
╵
ERRO[0561] terraform invocation failed in .../fe-infra/tf/prod/7_atlas
ERRO[0561] 1 error occurred:
	* [.../fe-infra/tf/prod/7_atlas] exit status 1
``` 

Remove the `aws_vpc_endpoint.ptfe_service` service from state:

`tg state rm aws_vpc_endpoint.ptfe_service`

then reimport it:

`tg import aws_vpc_endpoint.ptfe_service vpce-############`

`tg apply --target=mongodbatlas_privatelink_endpoint_service.atlas_to_subsystem`