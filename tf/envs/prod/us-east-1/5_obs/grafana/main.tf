terraform {
  required_providers {
    grafana = {
      source  = "grafana/grafana"
      version = "1.28.2"
    }
    local = {
      source  = "hashicorp/local"
      version = "~> 2.1"
    }
  }
}

variable "grafana_auth" {
  type = string
}

variable "grafana_url" {
  type = string
}

provider "grafana" {
  url  = var.grafana_url
  auth = var.grafana_auth
}

resource "grafana_folder" "folder" {
  title = "hub-fe-us"
}

variable "prometheus_default" {
  type = map(string)
  default = {
    datasource_uid = "dejt67cg60kxsa"
  }
}

locals {

  module_common_path = "${path.module}/.terraform/modules/commons/tf/modules/grafana/modules"

  dashboard_configs = {
    "${path.module}/../../../../shared/5_obs/grafana/templates/0-fe-general-cluster-dashboard.json" = [
      {
        datasource = var.prometheus_default.datasource_uid
        dashboard_title = "fe-cluster-metrics"
        add_modules     = []
      }
    ]
  }
}

module "commons" {
  source            = "**************:inv-cloud-platform/infra-com//tf/modules/grafana?ref=main"
  grafana_url       = var.grafana_url
  grafana_auth      = var.grafana_auth
  dashboard_configs = local.dashboard_configs
  grafana_folder_id = grafana_folder.folder.id
}