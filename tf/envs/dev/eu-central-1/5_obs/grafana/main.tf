terraform {
  required_providers {
    grafana = {
      source  = "grafana/grafana"
      version = "1.28.2"
    }
    local = {
      source  = "hashicorp/local"
      version = "~> 2.1"
    }
  }
}

variable "grafana_auth" {
  type = string
}

variable "grafana_url" {
  type = string
}

provider "grafana" {
  url  = var.grafana_url
  auth = var.grafana_auth
}

resource "grafana_folder" "folder" {
  title = "hub-fe-eu"
}

### Prometheus 
variable "prometheus_dev" {
  type = map(string)
  default = {
    datasource_uid = "edyhqnpsqypkwe"
  }
}
variable "prometheus_prod" {
  type = map(string)
  default = {
    datasource_uid = "ee50a5xpldzwgf"
  }
}

locals {

  module_common_path = "${path.module}/.terraform/modules/commons/tf/modules/grafana/modules"

  dashboard_configs = {
  }
}

module "commons" {
  source            = "**************:inv-cloud-platform/infra-com//tf/modules/grafana?ref=main"
  grafana_url       = var.grafana_url
  grafana_auth      = var.grafana_auth
  dashboard_configs = local.dashboard_configs
  grafana_folder_id = grafana_folder.folder.id
}