terraform {
  required_providers {
    grafana = {
      source  = "grafana/grafana"
      version = "1.28.2"
    }
  }
}

provider "grafana" {
  url  = var.obs_config.grafana_url
  auth = var.obs_config.grafana_auth
}

locals {
  dp_scrape_configs = <<-EOF
EOF
}

module "obs" {
  source                       = "**************-vnt:inv-cloud-platform/infra-com//tf/modules/obs?ref=v0.16.1"
  common_tags                  = var.common_tags
  obs_config                   = merge(var.obs_config, var.grafana_url)
  scrape_configs               = local.dp_scrape_configs
  grafana_dashboards_directory = "${path.module}/dashboards"
}

