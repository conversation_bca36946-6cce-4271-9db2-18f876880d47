## VPN vs Boundary

By default you will need to be on VPN to reach https://grafana.prod.is360.io/

You can use Bundary as an alternative to this with the following steps:

Set grafana hostname as your localhost
```
echo 127.0.0.1 grafana.prod.is360.io >> /etc/hosts
```

Connect to grafana target via Boundary (previously authenticated with Boundary)
```
boundary connect -target-scope-name core_infra -target-name "grafana prod" -tls-server-name grafana.prod.is360.io -tls-insecure -listen-port=4434 &
```

Set grafana_url variable to overwrite the default value
```
export TF_VAR_grafana_url='{"grafana_url"="https://grafana.prod.is360.io:4434/"}'
```
