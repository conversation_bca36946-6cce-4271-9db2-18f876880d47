resource "mongodbatlas_project" "wsm" {
  name                         = "${var.common_tags.env}-${var.common_tags.subsystem}-wsm"
  org_id                       = local.atlas_org_id
  with_default_alerts_settings = true

  is_collect_database_specifics_statistics_enabled = true
  is_data_explorer_enabled                         = true
  is_performance_advisor_enabled                   = true
  is_schema_advisor_enabled                        = true
}

resource "mongodbatlas_advanced_cluster" "wsm" {
  project_id                     = mongodbatlas_project.wsm.id
  name                           = "ava"
  cluster_type                   = "REPLICASET"
  backup_enabled                 = true
  pit_enabled                    = true
  termination_protection_enabled = true

  replication_specs {
    region_configs {
      electable_specs {
        instance_size = "M30"
        node_count    = 3
      }
      auto_scaling {
        disk_gb_enabled            = true
        compute_enabled            = true
        compute_scale_down_enabled = true

        compute_min_instance_size = "M30"
        compute_max_instance_size = "M50"
      }
      provider_name = "AWS"
      priority      = 7
      region_name   = "US_EAST_1"
    }
  }

  mongo_db_major_version = "8.0"
  version_release_system = "LTS"

  lifecycle {
    ignore_changes = [
      replication_specs[0].region_configs[0].electable_specs[0].instance_size,
      replication_specs[0].region_configs[0].auto_scaling[0].compute_min_instance_size,
      replication_specs[0].region_configs[0].auto_scaling[0].compute_max_instance_size
    ]
  }
}

resource "mongodbatlas_privatelink_endpoint" "wsm" {
  project_id    = mongodbatlas_project.wsm.id
  provider_name = "AWS"
  region        = "US_EAST_1"
}

resource "aws_vpc_endpoint" "wsm" {
  vpc_id       = data.aws_vpc.vpc.id
  service_name = mongodbatlas_privatelink_endpoint.wsm.endpoint_service_name
  auto_accept  = true

  vpc_endpoint_type = "Interface"

  # can't be turned on until after the acceptance
  # private_dns_enabled = true

  subnet_ids         = data.aws_subnets.private_ids.ids
  security_group_ids = [aws_security_group.allow_all_vpc.id]


  tags = merge(
    var.common_tags,
    { "Name" = "${var.common_tags.env}-van-${var.common_tags.subsystem}-wsm-atlas" }
  )
}

resource "mongodbatlas_privatelink_endpoint_service" "wsm_atlas_to_subsystem" {
  project_id          = mongodbatlas_privatelink_endpoint.wsm.project_id
  private_link_id     = mongodbatlas_privatelink_endpoint.wsm.private_link_id
  endpoint_service_id = aws_vpc_endpoint.wsm.id
  provider_name       = "AWS"
}