data "aws_vpc" "vpc" {
  tags = { Name = "${var.common_tags.env}-van-vpc" }
}

data "aws_subnets" "private_ids" {
  tags = {
    Name = "${var.common_tags.env}-van-${var.common_tags.subsystem}-private*"
  }
}

data "aws_subnet" "private" {
  for_each = toset(data.aws_subnets.private_ids.ids)
  id       = each.value
}

// TODO: can be more restrictive
resource "aws_security_group" "allow_all_vpc" {
  name        = "atlas-${var.common_tags.env}-${var.common_tags.subsystem}-allow_tls"
  description = "Allow TLS inbound traffic"
  vpc_id      = data.aws_vpc.vpc.id

  ingress {
    description = "All from vpc"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = [data.aws_vpc.vpc.cidr_block]
  }

  egress {
    description = "All to vpc"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = [data.aws_vpc.vpc.cidr_block]
  }

  tags = merge(
    var.common_tags,
    { "Name" = "${var.common_tags.env}-${var.common_tags.subsystem}-allow-all-private-subnets" }
  )
}

resource "mongodbatlas_privatelink_endpoint" "ple" {
  project_id    = mongodbatlas_project.project.id
  provider_name = "AWS"
  region        = "US_EAST_1"
}

resource "aws_vpc_endpoint" "ptfe_service" {
  vpc_id       = data.aws_vpc.vpc.id
  service_name = mongodbatlas_privatelink_endpoint.ple.endpoint_service_name
  auto_accept  = true

  vpc_endpoint_type = "Interface"

  # can't be turned on until after the acceptance
  # private_dns_enabled = true

  subnet_ids         = data.aws_subnets.private_ids.ids
  security_group_ids = [aws_security_group.allow_all_vpc.id]

  tags = merge(
    var.common_tags,
    { "Name" = "${var.common_tags.env}-van-${var.common_tags.subsystem}-atlas-${var.common_tags.env}" }
  )

  lifecycle { ignore_changes = [dns_entry] }
}

resource "mongodbatlas_privatelink_endpoint_service" "atlas_to_subsystem" {
  project_id          = mongodbatlas_privatelink_endpoint.ple.project_id
  private_link_id     = mongodbatlas_privatelink_endpoint.ple.private_link_id
  endpoint_service_id = aws_vpc_endpoint.ptfe_service.id
  provider_name       = "AWS"
}
