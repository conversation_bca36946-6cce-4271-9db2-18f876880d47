terraform {
  required_providers {
    mongodbatlas = {
      source  = "mongodb/mongodbatlas"
      version = ">= 1.15"
    }
  }
}

# assumes MONGODB_ATLAS_PUBLIC_KEY and MONGODB_ATLAS_PRIVATE_KEY are set in env
provider "mongodbatlas" {
}

locals {
  atlas_org_id = "62d58d280ef57c1a87fb4131"
}

resource "mongodbatlas_project" "project" {
  name                         = "${var.common_tags.env}-${var.common_tags.subsystem}"
  org_id                       = local.atlas_org_id
  with_default_alerts_settings = true

  is_collect_database_specifics_statistics_enabled = true
  is_data_explorer_enabled                         = true
  is_performance_advisor_enabled                   = true
  is_schema_advisor_enabled                        = true
}

resource "mongodbatlas_cluster" "fm" {
  project_id   = mongodbatlas_project.project.id
  name         = "${var.common_tags.env}-${var.common_tags.subsystem}-fm"
  cluster_type = "REPLICASET"

  replication_specs {
    num_shards = 1
    regions_config {
      region_name     = "US_EAST_1"
      electable_nodes = 3
      priority        = 7
      read_only_nodes = 0
    }
  }

  cloud_backup                 = true
  auto_scaling_disk_gb_enabled = true
  auto_scaling_compute_enabled = true
  mongo_db_major_version       = "7.0"
  version_release_system       = "LTS"

  # Provider Settings "block"
  provider_name                                   = "AWS"
  disk_size_gb                                    = 100
  provider_instance_size_name                     = "M10"
  provider_auto_scaling_compute_min_instance_size = "M10"
  provider_auto_scaling_compute_max_instance_size = "M20"

  lifecycle { ignore_changes = [provider_instance_size_name] }
}

resource "mongodbatlas_cluster" "am" {
  project_id   = mongodbatlas_project.project.id
  name         = "${var.common_tags.env}-${var.common_tags.subsystem}-am"
  cluster_type = "REPLICASET"

  replication_specs {
    num_shards = 1
    regions_config {
      region_name     = "US_EAST_1"
      electable_nodes = 3
      priority        = 7
      read_only_nodes = 0
    }
  }

  cloud_backup                 = true
  auto_scaling_disk_gb_enabled = true
  auto_scaling_compute_enabled = true
  mongo_db_major_version       = "7.0"
  version_release_system       = "LTS"

  # Provider Settings "block"
  provider_name                                   = "AWS"
  disk_size_gb                                    = 100
  provider_instance_size_name                     = "M10"
  provider_auto_scaling_compute_min_instance_size = "M10"
  provider_auto_scaling_compute_max_instance_size = "M20"

  lifecycle { ignore_changes = [provider_instance_size_name] }
}
