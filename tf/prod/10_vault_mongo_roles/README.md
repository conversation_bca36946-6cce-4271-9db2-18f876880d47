# Vault Mongo DB Backend Connection

Managed by resource type `vault_database_secret_backend_connection`. This is a per Mongo project Connection

## First time setup
1. Login to Mongo Atlas UI
2. Navigate to the `Project > Access Manager > Project Access  > Invite to Project` and add Project Data Access Admin permissions to Vault's API Key called `vault-dev-dp-dbengine`

![alt text](image.png)

3. Apply the Terraform
4. Manually set private key for the connection so this stays out of Terraform state. e.g
```
  vault write database/${ENV}-mongodbatlas/config/${PROJECT_NAME} \ 
    plugin_name=mongodbatlas-database-plugin \
    private_key="*" 

```

