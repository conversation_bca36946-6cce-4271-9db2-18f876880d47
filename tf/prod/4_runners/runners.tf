provider "kubernetes" {
  host                   = data.aws_eks_cluster.cluster.endpoint
  cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
  token                  = data.aws_eks_cluster_auth.cluster.token
}

data "aws_eks_cluster" "cluster" {
  name = "${var.common_tags.env}-${var.common_tags.subsystem}"
}

data "aws_eks_cluster_auth" "cluster" {
  name = "${var.common_tags.env}-${var.common_tags.subsystem}"
}

module "arc_repo" {
  source = "**************-vnt:inv-cloud-platform/infra-com//tf/modules/arc_repo?ref=v0.12.3"

  github_app_id              = "826853"
  github_app_installation_id = "47233267"
  github_app_private_key     = file("${path.module}/vnt-prod-fe-arc.private-key.pem")
  runner_image               = "633377509572.dkr.ecr.us-east-1.amazonaws.com/infra-arcimage-base:latest"

  common_tags = var.common_tags
  repo_name   = "${var.common_tags.subsystem}-deploy"

}

module "arc_repo_infra" {
  source = "**************-vnt:inv-cloud-platform/infra-com//tf/modules/arc_repo?ref=v0.12.3"

  github_app_id              = "826853"
  github_app_installation_id = "47233267"
  github_app_private_key     = file("${path.module}/vnt-prod-fe-arc.private-key.pem")

  common_tags  = var.common_tags
  repo_name    = "${var.common_tags.subsystem}-infra"
  runner_image = "633377509572.dkr.ecr.us-east-1.amazonaws.com/infra-arcimage-base:latest"

}
