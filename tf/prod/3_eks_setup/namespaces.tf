provider "kubernetes" {
  host                   = data.aws_eks_cluster.cluster.endpoint
  cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
  token                  = data.aws_eks_cluster_auth.cluster.token
}

data "aws_eks_cluster" "cluster" {
  name = "${var.common_tags.env}-${var.common_tags.subsystem}"
}

data "aws_eks_cluster_auth" "cluster" {
  name = "${var.common_tags.env}-${var.common_tags.subsystem}"
}

resource "kubernetes_namespace" "monitoring_logging_namespace" {
  metadata {
    name = "obs"
  }
}

resource "kubernetes_namespace" "subsystem" {
  metadata {
    name = var.common_tags.subsystem
  }
}

resource "kubernetes_namespace" "apps" {
  for_each = var.apps
  metadata {
    name = "${var.common_tags.subsystem}-${each.key}"
  }
}

