module "eks_roles_cdm" {
  source = "**************-vnt:inv-cloud-platform/infra-com//tf/modules/eks_roles?ref=v0.11.4"

  cluster_arn = data.aws_eks_cluster.cluster.arn
  namespace   = "fe-cdm"
}

data "aws_caller_identity" "current" {}

module "eks_roles_cdm_bb_pipeline" {
  source = "**************-vnt:inv-cloud-platform/infra-com//tf/modules/eks_roles?ref=v0.11.4"

  cluster_arn = data.aws_eks_cluster.cluster.arn
  namespace   = "fe-cdm"
  allow_entities = ["pipeline"]
  entities = {
    pipeline = {
      rule = {
        "api_groups" = [
          "*"]
        "resources" = [
          "*"]
        "verbs" = [
          "*"]
      }
    },
  }
  assume_role_policy = <<POLICY
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {
                "Federated": "arn:aws:iam::${data.aws_caller_identity.current.account_id}:oidc-provider/${local.identity_provider_url}"
            },
            "Action": "sts:AssumeRoleWithWebIdentity",
            "Condition": {
                "StringLike": {
                    "api.bitbucket.org/2.0/workspaces/veederrootsoftware/pipelines-config/identity/oidc:sub": "{${local.bb_pipeline_repo}}:*"
                }
            }
        }
    ]
}
POLICY
}

locals {
  identity_provider_url = "api.bitbucket.org/2.0/workspaces/veederrootsoftware/pipelines-config/identity/oidc"
  bb_pipeline_repo      = "3b38fd68-cece-4774-9046-0ab7681a3444"
}

