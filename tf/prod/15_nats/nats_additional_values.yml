config:
  gateway:
    enabled: true
    port: 7222
    connect_retries: 1
    merge:
      gateways:
        - name: nats-ia
          url: "nats://nats.ia.svc.cluster.local:7222"
        - name: nats-cp
          url: "nats://nats.cp.svc.cluster.local:7222"
        - name: nats-dp
          url: "nats://nats.dp.svc.cluster.local:7222"
        - name: nats-fe
          url: "nats://nats.fe.svc.cluster.local:7222"
  merge:
    authorization:
      users:
        - nkey: UC2GYUYYQ77XXCHPS2TMRGAKQXMKUB5ONX245CG7YPQSLU4SN5T5SA5K
        - nkey: "UDH3UB7U26VTIHE7UFBXKYAIMDAAMPH2BYEBO6VDXKIIHKFBWJFT3FTG" # fe-cdm/console-properties
          permissions:
            subscribe: 
              allow:
                - "_INBOX.>"
                - "dp-ucc-props-in.device-properties.>"
        - nkey: "UDGAPGN52V3FDMDX5ZGZE4ISQZKMG4HIUPLEJGCZC34HTARGJDZAEZET" # fe-cdm/console-telemetry
          permissions:
            subscribe: 
              allow:
                - "_INBOX.>"
                - "dp-ucc-telem-in.tls-alarmManagement.>"
                - "dp-ucc-telem-in.tls-changeManagement.>"
                - "dp-ucc-telem-in.tls-predictiveMaintenance.>"
                - "dp-ucc-telem-in.tls-inventory.>"
                - "dp-ucc-telem-in.tls-delivery.>"
                - "dp-ucc-telem-in.tls-softwareMaintenance.>"
                - "dp-ucc-telem-in.tls-compliance.>"
        - nkey: "UC75PRXTTNVA52QOCEBEANVDYOM52MSQ4G3FR3OY4DGYHTZKMFF5VXJO" # fe-ev/fe-ev-csms-api
          permissions:
            publish:
              allow:
                - "$JS.API.STREAM.INFO.fe-ev-csms-api"
                - "fe-ev-csms-api.*.*.locationUpdate.>"
                - "fe-ev-csms-api.*.*.sessionUpdate.>"
                - "fe-ev-csms-api.*.*.cdrUpdate.>"
                - "fe-ev-csms-api.*.*.commandUpdate.>"
                - "fe-ev-csms-api.*.*.tariffUpdate.>"
            subscribe: 
              allow:
                - "_INBOX.>"
                - "fe-ev-csms-api.>"
        - nkey: "UDQNSEGQN64FOWJCHYVQ3DVVKX6R5SRIBWEFJ3OKCAN7IRYRTSA7FHWH" # fe-ev/fe-ev-csms-io 
          permissions:
            subscribe: 
              allow:
                - "_INBOX.>"
                - "dp-ucc-telem-in.eds-ev.ocpiCommand.>"
        - nkey: "UDKBTQQSB2H23AVYY2BA35R7KFTC3MRDBRAFRO4LGFSBEP2T5SNVEVQ2" # fe-ev/fe-ev-cdr-in
          permissions:
            subscribe: 
              allow:
                - "_INBOX.>"
                - "fe-ev-csms-api.*.*.cdrUpdate.>"
        - nkey: "UBUECFWUPMX2UH5ZHUCPMRSWPYY3S2GUPK3W2EKLJEUWXWVUW5OLBESR" # fe-ev/fe-ev-tariff-in
          permissions:
            subscribe: 
              allow:
                - "_INBOX.>"
                - "fe-ev-csms-api.*.*.tariffUpdate.>"
        - nkey: "UABBP6NTT5GJDOVFKR5UT3V6WAVN2SXLEJZ2WHGJXSLYQRS4MFIRCUJU" # fe-ev/fe-ev-session-in
          permissions:
            subscribe: 
              allow:
                - "_INBOX.>"
                - "fe-ev-csms-api.*.*.sessionUpdate.>"
        - nkey: "UAJU5HXZIJJPHYSJZ7T55IGPIWKTXTQYJXWMDQWRRCXJC3TYOGDVAHIV" # fe-ev/fe-ev-obj-update-io
          permissions:
            subscribe: 
              allow:
                - "_INBOX.>"
                - "fe-ev-csms-api.*.*.locationUpdate.>"
                - "fe-ev-csms-api.*.*.sessionUpdate.>"
                - "fe-ev-csms-api.*.*.cdrUpdate.>"
                - "fe-ev-csms-api.*.*.commandUpdate.>"
                - "fe-ev-csms-api.*.*.tariffUpdate.>"
        - nkey: "UCZCBYJYKG6FD7C35GGPIPQUBU6XCNNFQJBGRYIHCUY4TKLARJWHRKYK" # fe-ev/fe-ev-finadv-io
          permissions:
            subscribe: 
              allow:
                - "_INBOX.>"
                - "dp-ucc-telem-in.eds-ev.finAdv.>"
        - nkey: "UBOT2XBM26I3O5NAQNACYLUGEGB6NQWLRT5URJLF4KRVCNJBQKQZIQF4" # fe-ev/fe-ev-e2e
          permissions:
            subscribe: 
              allow:
                - "_INBOX.>"
                - "dp-ucc-telem-in.eds-ev.finAdv.>"
        - nkey: "UAPLNWZF35LFAR3TAKMEMYXPCWB7DXS7O2UIITDZLOEVVAPSX54ECMMY" # fe-ev/fe-ev-location-in
          permissions:
            subscribe: 
              allow:
                - "_INBOX.>"
                - "fe-ev-csms-api.*.*.locationUpdate.>"
        - nkey: "UCS5PO6R4JZCGZTWH5N7BTDABFIRF2GQVDEZHSPFOZ7E5MDW4L7EWFCX" # fe-ev/fe-ev-smsrcpt-io-nkey
          permissions:
            subscribe: 
              allow:
                - "_INBOX.>"
                - "dp-ucc-telem-in.eds-finadv.rcptFwdReq.>"
        - nkey: "UDDKGZOPG3BVVZ66EVYMUBLDGVHXWPGKEQZZ7CGF5KBZBDBBLB3LUYH3" # vault prod/prod-fe/fe-ppe-logs-api-nats-nkey-pub
          permissions:
            publish: 
              allow:
                - "dp-ucc-props-in.device-properties.>"
                - "$JS.API.STREAM.INFO.dp-ucc-props-in"
            
statefulSet:
  name: nats-fe

service:
  ports:
    gateway:
      enabled: true
  patch:
    - op: add
      path: "/metadata/annotations"
      value:
        "io.cilium/global-service": "true"
        "io.cilium/shared-service": "true"
