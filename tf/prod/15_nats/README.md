## NATS

### Create credentions

Make sure that you have the [`nk`](https://docs.nats.io/using-nats/nats-tools/nk) cli installed

Then run `nk -gen user -pubout`
> Key starting with `S` is a private key, and the one starting with `U` us a public key

Get the `public key` and place in the [`nats_additional_values.yml`](nats_additional_values.yml) under `config.merge.authorization`

Add the `private key` to a secret where the service can fetch. We use `vault` with th following keys
- `{service-name}-nats-nkey` for private key
- `{service-name}-nats-nkey-pub` for public key
