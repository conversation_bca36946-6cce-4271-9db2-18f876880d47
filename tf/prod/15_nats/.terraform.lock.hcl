# This file is maintained automatically by "terraform init".
# Manual edits may be lost in future updates.

provider "registry.terraform.io/gavin<PERSON><PERSON>/kubectl" {
  version     = "1.19.0"
  constraints = ">= 1.14.0"
  hashes = [
    "h1:9QkxPjp0x5FZFfJbE+B7hBOoads9gmdfj9aYu5N4Sfc=",
    "zh:1dec8766336ac5b00b3d8f62e3fff6390f5f60699c9299920fc9861a76f00c71",
    "zh:43f101b56b58d7fead6a511728b4e09f7c41dc2e3963f59cf1c146c4767c6cb7",
    "zh:4c4fbaa44f60e722f25cc05ee11dfaec282893c5c0ffa27bc88c382dbfbaa35c",
    "zh:51dd23238b7b677b8a1abbfcc7deec53ffa5ec79e58e3b54d6be334d3d01bc0e",
    "zh:5afc2ebc75b9d708730dbabdc8f94dd559d7f2fc5a31c5101358bd8d016916ba",
    "zh:6be6e72d4663776390a82a37e34f7359f726d0120df622f4a2b46619338a168e",
    "zh:72642d5fcf1e3febb6e5d4ae7b592bb9ff3cb220af041dbda893588e4bf30c0c",
    "zh:9b12af85486a96aedd8d7984b0ff811a4b42e3d88dad1a3fb4c0b580d04fa425",
    "zh:a1da03e3239867b35812ee031a1060fed6e8d8e458e2eaca48b5dd51b35f56f7",
    "zh:b98b6a6728fe277fcd133bdfa7237bd733eae233f09653523f14460f608f8ba2",
    "zh:bb8b071d0437f4767695c6158a3cb70df9f52e377c67019971d888b99147511f",
    "zh:dc89ce4b63bfef708ec29c17e85ad0232a1794336dc54dd88c3ba0b77e764f71",
    "zh:dd7dd18f1f8218c6cd19592288fde32dccc743cde05b9feeb2883f37c2ff4b4e",
    "zh:ec4bd5ab3872dedb39fe528319b4bba609306e12ee90971495f109e142d66310",
    "zh:f610ead42f724c82f5463e0e71fa735a11ffb6101880665d93f48b4a67b9ad82",
  ]
}

provider "registry.terraform.io/grafana/grafana" {
  version     = "3.7.0"
  constraints = "~> 3.7.0"
  hashes = [
    "h1:0IZvNOQoXjY7HreNBmaqMzIhyfGtVPtnbUACRDc78so=",
    "zh:26d725b30360de5365d356c42cea31d5202caf782db8bd346d0ecf77d18ac9c5",
    "zh:2d431a39f057f183ba5fa423ff78486eb2cac6c18fdf5b2c4d4209fbd8960121",
    "zh:43e8e58733ac9dcaa91377c50e6cdcd1bb39e76e9a9aafb8cae4aebf038ec308",
    "zh:5ecaf4aa7f013aac7fa5e3ab016b9abb9a8b7cffcdc6c82019c109fa8333b2ee",
    "zh:6543e61c1a8315553e5e7c1cdfe5ba2f596d37590c4d3aa0d0456db32babc1a0",
    "zh:6b071ce356844e94c97d85c9af67eda914fabf56c9b8483cfb53c0dd39bbb088",
    "zh:6d9996f23da3f569f0d5321dc328afa64edf19450e00171ae4a231d31af03b54",
    "zh:6fede397e9e7ef9485eedfd95f5b5793f6c4a455b56b0439ca50e723779e11d0",
    "zh:742d5c1b4e40bec167c13f9631999ef53f6b344ca264a1acd24bdc93a36dcde2",
    "zh:749e11cdc0dece4ec46687673c0a763f0013e124e7b0296072841514740e7097",
    "zh:826e2b2c48b8b732739bc8a4d4cf685855ee1aa803799de798b48a5e64ca8536",
    "zh:93aa4836d8fd7bb6ca5fcd95edbe6654e0e691bb39b3d903644d7e94fba5cd2f",
    "zh:99a54e4eda338aa39710bb25591d8fd209afa16b471d4d2995631b415aa8cedd",
    "zh:9aadfd03a8a13c337f8ff6a9e11b0ddc1c33683a799d89558fd640cdb200ea6e",
    "zh:9e28385102bccaf9dee2396462cba9ed3777ad8cba486c74c4591aa93eee4ace",
    "zh:b7593e2e2ecf87c7dccb59c77cd5daf31865ef8dfe043cb23a56327dc65c1f2b",
    "zh:c4623e2007ea1fc370ee9f2a94f3c70392a8b4a5e037984b6693d2ef1f38504e",
    "zh:c788b29c98b39fa4d9dc76fc55d351774a261e98fe78dcc2bcf7d6fb36681cf1",
    "zh:cd5800d77269aa6570a014a0dfecdb16aba4d626dc6d81b41d60c5ed6c0c3120",
    "zh:d5e891bbfa980ce265100a2d48178804fe7e0823bc2c13f32f0a34a3a82facdc",
  ]
}

provider "registry.terraform.io/hashicorp/aws" {
  version = "5.90.0"
  hashes = [
    "h1:F7mwxvg2vgUZ3GEMPu5qktswPYoAHic1Gab+veOyvM8=",
    "zh:0ed246595c4ffb3ea3649528ff171503db208fb20be5f750b8e359d17ee72b60",
    "zh:1d5c500913b5df0fbf5e8143354aecc736cc4e66d58d4ab17deb24b721ab743a",
    "zh:337f3511335e6e32431548913d1973ae077d1a4c2f77677675c92c60cd2f5e0a",
    "zh:624762ff78819aee434d6c3e6c79eb93c91060be2df4f45f9014272a60b5d608",
    "zh:7f4ab9bcd667e38b7d7b7aa1068535f01eef3656ecd422acccbe8238d377a15a",
    "zh:84542ce0403cacee245c1a159169cc0ddb965d7d734216f9eb0bb3ff0a0bae36",
    "zh:85dd27e39f2c3ab13cb5c02236b810893bd90ec6da33fabaa7ab6d116accfa10",
    "zh:9b12af85486a96aedd8d7984b0ff811a4b42e3d88dad1a3fb4c0b580d04fa425",
    "zh:a0cf76959ade91958b08d186f5bcdc403395fa635f21912464da40bc7a5db4ff",
    "zh:a9a48f9f7f4122b6a44b7273b4cc54020887f7346f50286d7da1278cca2ee952",
    "zh:c119b826e334aac2d03ea561774dad536ccd6449e2a4f42b3af100623ae02679",
    "zh:d4204ca7f1295732660c70db4ea04c3ae1f7e1ac82c0ec9d0dc549493bc45e7a",
    "zh:d95f89181d12ebab1b1f964274d29795e1e6e2d112ea97caffd8a7f1326a922d",
    "zh:e529c7be1037f1a9a733fc0bcbbdcc58fc44f85ed343f891e5c584b2ef56fd5c",
    "zh:e541c135514a6727f20410a9a52c06cb71b4ddadaf2a41da28d599fb1c442845",
  ]
}

provider "registry.terraform.io/hashicorp/helm" {
  version     = "2.17.0"
  constraints = "~> 2.15"
  hashes = [
    "h1:K5FEjxvDnxb1JF1kG1xr8J3pNGxoaR3Z0IBG9Csm/Is=",
    "zh:06fb4e9932f0afc1904d2279e6e99353c2ddac0d765305ce90519af410706bd4",
    "zh:104eccfc781fc868da3c7fec4385ad14ed183eb985c96331a1a937ac79c2d1a7",
    "zh:129345c82359837bb3f0070ce4891ec232697052f7d5ccf61d43d818912cf5f3",
    "zh:3956187ec239f4045975b35e8c30741f701aa494c386aaa04ebabffe7749f81c",
    "zh:66a9686d92a6b3ec43de3ca3fde60ef3d89fb76259ed3313ca4eb9bb8c13b7dd",
    "zh:88644260090aa621e7e8083585c468c8dd5e09a3c01a432fb05da5c4623af940",
    "zh:a248f650d174a883b32c5b94f9e725f4057e623b00f171936dcdcc840fad0b3e",
    "zh:aa498c1f1ab93be5c8fbf6d48af51dc6ef0f10b2ea88d67bcb9f02d1d80d3930",
    "zh:bf01e0f2ec2468c53596e027d376532a2d30feb72b0b5b810334d043109ae32f",
    "zh:c46fa84cc8388e5ca87eb575a534ebcf68819c5a5724142998b487cb11246654",
    "zh:d0c0f15ffc115c0965cbfe5c81f18c2e114113e7a1e6829f6bfd879ce5744fbb",
    "zh:f569b65999264a9416862bca5cd2a6177d94ccb0424f3a4ef424428912b9cb3c",
  ]
}

provider "registry.terraform.io/hashicorp/kubernetes" {
  version     = "2.32.0"
  constraints = "~> 2.32.0"
  hashes = [
    "h1:HqeU0sZBh+2loFYqPMFx7jJamNUPEykyqJ9+CkMCYE0=",
    "zh:0e715d7fb13a8ad569a5fdc937b488590633f6942e986196fdb17cd7b8f7720e",
    "zh:495fc23acfe508ed981e60af9a3758218b0967993065e10a297fdbc210874974",
    "zh:4b930a8619910ef528bc90dae739cb4236b9b76ce41367281e3bc3cf586101c7",
    "zh:5344405fde7b1febf0734052052268ee24e7220818155702907d9ece1c0697c7",
    "zh:92ee11e8c23bbac3536df7b124456407f35c6c2468bc0dbab15c3fc9f414bd0e",
    "zh:a45488fe8d5bb59c49380f398da5d109a4ac02ebc10824567dabb87f6102fda8",
    "zh:a4a0b57cf719a4c91f642436882b7bea24d659c08a5b6f4214ce4fe6a0204caa",
    "zh:b7a27a6d11ba956a2d7b0f7389a46ec857ebe46ae3aeee537250e66cac15bf03",
    "zh:bf94ce389028b686bfa70a90f536e81bb776c5c20ab70138bbe5c3d0a04c4253",
    "zh:d965b2608da0212e26a65a0b3f33c5baae46cbe839196be15d93f70061516908",
    "zh:f441fc793d03057a17af8bdca8b26d54916645bc5c148f54e22a54ed39089e83",
    "zh:f569b65999264a9416862bca5cd2a6177d94ccb0424f3a4ef424428912b9cb3c",
  ]
}
