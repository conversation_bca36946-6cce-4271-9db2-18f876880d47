variable "common_tags" { // need to rerun all modules to apply any changes
  type = map(string)
  default = {
    env          = "prod"
    region       = "us"
    subsystem    = "fe"
    creator      = "fe-infra"
    product-line = "Infrastructure" // to match global IS360 tag
    architecture = "vandura"
  }
}

variable "apps" { // we'll create namespaces for each app
  type = set(string)
  default = [
    "am",
    "wm",
    "fm",
    "cdm",
    "wsm",
    "tlstech",
    "devapp",
    "ev",
    "ucc",
    "ppe",
    "pcn",
    "dispctlr"
  ]
}

variable "region" {
  type    = string
  default = "us-east-1"
}

variable "monitoring_service_type" {
  type    = string
  default = "NodePort"
}


### Prometheus variables
variable "prometheus" {
  type = map(string)
  default = {
    datasource_uid = "NyHmeWBnz"
  }
}


variable "grafana_url" {
  type = map(string)
  default = {
    grafana_url = "https://grafana.prod.is360.io/"
  }
}

### common obs config
variable "obs_config" {
  type = map(string)
  default = {
    amp_remote_write_url  = "https://aps-workspaces.us-east-1.amazonaws.com/workspaces/ws-acc4c7f8-ab4a-407d-adc8-d2c36b9b129f/api/v1/remote_write"
    grafana_auth          = "glsa_d5qAKjQETXTrDsvYh8dv3RfRZKsiuXQj_6dcc5f2f"
    grafana_url           = "https://grafana.prod.is360.io/"
    contact_point_webhook = "https://vontier.webhook.office.com/webhookb2/ab147851-b184-4732-857c-80dd82c46cea@8df22a8d-9153-489d-950c-a20cccb65620/IncomingWebhook/e97d1fa6ffa34243b8356bb2a4482f2e/afca3537-63c3-4847-aa54-edf4e4c0f93b/V2u-FVrmqRD8RjGJNJfHlnkSif4zQNlv3kY0XKd_hV4I81"
    loki_push_url         = "http://grafana.prod.is360.io:3100/loki/api/v1/push"
    grafana_public_ip     = "grafana.prod.is360.io"
    grafana_team          = "Connectar"
  }
}

### Boundary and Vault variables
variable "vault_boundary_config" {
  type = map(string)
  default = {
    vault_url           = "https://vault.dev.is360.io:8200"
    vault_role_name     = "kubernetes-prod-fe"
    vault_policy_name   = "dev" //vault developer policy
    vault_auth_mount    = "kubernetes-prod-fe"
    fe_secrets_name     = "fe-secrets"
    am_secrets_name     = "fe-am-secrets"
    fm_secrets_name     = "fe-fm-secrets"
    wm_secrets_name     = "fe-wm-secrets"
    ev_secrets_name     = "fe-ev-secrets"
  }
}

variable "mongo_db_name" {
  type    = string
  default = "prod-mongodbatlas"
}

variable "vault_mongo_public_key" {
  type = string
  default = "fmhbblxd"
}

