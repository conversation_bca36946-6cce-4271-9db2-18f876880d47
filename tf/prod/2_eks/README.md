If you get an error that looks like:

```
│ Error: Invalid for_each argument
│
│   on .terraform/modules/eks/modules/eks-managed-node-group/main.tf line 448, in resource "aws_iam_role_policy_attachment" "this":
│  448:   for_each = var.create && var.create_iam_role ? toset(compact(distinct(concat([
│  449:     "${local.iam_role_policy_prefix}/AmazonEKSWorkerNodePolicy",
│  450:     "${local.iam_role_policy_prefix}/AmazonEC2ContainerRegistryReadOnly",
│  451:     var.iam_role_attach_cni_policy ? local.cni_policy : "",
│  452:   ], var.iam_role_additional_policies)))) : toset([])
│     ├────────────────
│     │ local.cni_policy is "arn:aws:iam::aws:policy/AmazonEKS_CNI_Policy"
│     │ local.iam_role_policy_prefix is "arn:aws:iam::aws:policy"
│     │ var.create is true
│     │ var.create_iam_role is true
│     │ var.iam_role_additional_policies is a list of string, known only after apply
│     │ var.iam_role_attach_cni_policy is true
```

	* `tg apply --target=module.eks.aws_iam_policy.s3_tf_state`
	* `tg apply --target=module.eks.aws_iam_policy.cloudwatch_iam_policy`

## Notes

* on first run callers will need to run `tg apply --target=module.eks.aws_iam_policy.s3_tf_state` then apply the rest of the module
* this will error out when fully applied on a cluster that has not executed the `eks_setup` module, simply execute that module then come back to this one to validate that the k8 cluster resources have automatically recovered. Re-apply if required.