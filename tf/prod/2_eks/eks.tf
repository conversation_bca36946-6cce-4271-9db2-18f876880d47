data "aws_caller_identity" "current" {}

data "aws_region" "current" {}

module "eks" {
  source = "**************-vnt:inv-cloud-platform/infra-com//tf/modules/eks?ref=v0.20.3"

  common_tags               = var.common_tags
  aws_lb_controller_enabled = true
  keda_enabled              = true
  # keda_iam_policies         = ["arn:aws:iam::aws:policy/AmazonSQSFullAccess"]

  iam_role_additional_policies = {}

  disk_size     = 50
  instance_type = "m5a.large"
  desired_size  = 5
  min_size      = 4
  max_size      = 5

  permissions = [
    {
      name               = "dev",
      verbs              = "['get', 'watch', 'list']"
      resources          = "['pods', 'pods/log', 'services']"
      apps_verbs         = "['get', 'watch', 'list']"
      apps_resources     = "['deployments','replicasets']"
      enable_portforward = false
    },
    {
      name               = "lead",
      verbs              = "['get', 'watch', 'list']"
      resources          = "['pods', 'pods/log', 'services', 'secrets']"
      apps_verbs         = "['get', 'watch', 'list']"
      apps_resources     = "['deployments', 'replicasets']"
      enable_portforward = true
    },
  ]

  aws_auth_roles = [
    {
      rolearn  = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/sso.i360.io-full-admin"
      username = "sso.i360.io-full-admin"
      groups   = ["system:masters"]
    },
    { # enable developers to admin cluster in dev environment, do not use in prod
      rolearn  = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/sso.i360.io-admin-power"
      username = "sso.i360.io-admin-power"
      groups   = ["system:masters"]
    },
    { # TODO: Remove this role and the above when we can scope dev access only to the subsystem namespace
      rolearn  = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/sso.i360.io-developer"
      username = "sso.i360.io-developer"
      groups   = ["system:masters"]
    },
    {
      rolearn  = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/sso.i360.io-fe-cdm-admin"
      username = "sso.i360.io-fe-cdm-admin"
      groups   = ["admin"]
    },
    {
      rolearn  = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/sso.i360.io-fe-cdm-pipeline"
      username = "sso.i360.io-fe-cdm-pipeline"
      groups   = ["admin"]
    },
  ]

  aws_auth_users = [
    {
      userarn  = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:user/<EMAIL>"
      username = "<EMAIL>"
      groups   = ["system:masters"]
    }
  ]

  prom_remote_write_url = "https://aps-workspaces.us-east-1.amazonaws.com/workspaces/ws-c3f49063-39d6-4e3e-bcbf-d5ca950782f3/api/v1/remote_write"
  
  teams_webhook_url = var.obs_config.contact_point_webhook
}
